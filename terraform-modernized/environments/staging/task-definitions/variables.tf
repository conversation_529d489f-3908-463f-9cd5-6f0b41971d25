# Variables for staging environment .NET Core task definitions

# Basic Configuration
variable "application" {
  description = "Application name"
  type        = string
  default     = "ais"
}

variable "service" {
  description = "Service name"
  type        = string
  default     = "processing-apps"
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "staging"
}

variable "build_number" {
  description = "Build number for deployment tracking"
  type        = string
}

variable "launched_by" {
  description = "Who launched this deployment"
  type        = string
}

variable "launched_on" {
  description = "When this deployment was launched"
  type        = string
}

variable "slack_contact" {
  description = "Slack contact for notifications"
  type        = string
}

variable "component" {
  description = "Component name"
  type        = string
  default     = "processing-apps"
}

variable "account_id" {
  description = "AWS account ID"
  type        = string
}

variable "regions_abbreviated" {
  description = "Map of region abbreviations"
  type        = map(string)
}

variable "processing_apps_component_id" {
  description = "Component ID for processing apps"
  type        = string
}

# Email Configuration
variable "email_smtp_user" {
  description = "SMTP username for email notifications"
  type        = string
  sensitive   = true
}

variable "email_smtp_password" {
  description = "SMTP password for email notifications"
  type        = string
  sensitive   = true
}

variable "email_to_emails" {
  description = "List of recipient email addresses"
  type        = list(string)
  default     = ["<EMAIL>"]
}

# Database Configuration
variable "db_host" {
  description = "Database host"
  type        = string
}

variable "db_database" {
  description = "Database name"
  type        = string
}

variable "db_user" {
  description = "Database username"
  type        = string
  sensitive   = true
}

variable "db_password" {
  description = "Database password"
  type        = string
  sensitive   = true
}

# DataOne Configuration
variable "dataone_s3_bucket" {
  description = "S3 bucket for DataOne file uploads"
  type        = string
}
