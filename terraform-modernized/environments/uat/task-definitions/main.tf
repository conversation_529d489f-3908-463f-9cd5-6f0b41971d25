# UAT environment task definitions for .NET Core DescribedVehicleExtract

terraform {
  required_providers {
    aws = {
      version = "3.76.0"
      source  = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.processing_apps_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

# Data sources for existing infrastructure
data "terraform_remote_state" "ecs-cluster" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/nonprod/processing-apps-cluster"
    region = var.region
  }
}

data "terraform_remote_state" "sns-alerts" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/nonprod/sns-alerts"
    region = var.region
  }
}

# SSM Parameters for .NET Core application configuration
module "ssm-parameters" {
  source = "../../../modules/dotnet-processing-apps/ssm-parameters"

  ssm_parameter_prefix = "/ais/describevehicleextract/${var.environment}"
  
  application = var.application
  service     = var.service
  environment = var.environment
  component   = var.component

  # Application settings
  active_desc_veh_save_directory = "/app/data/exports"

  # Email settings
  email_from_email    = "<EMAIL>"
  email_smtp_server   = "email-smtp.us-east-1.amazonaws.com"
  email_smtp_port     = "25"
  email_smtp_user     = var.email_smtp_user
  email_smtp_password = var.email_smtp_password
  email_to_emails     = var.email_to_emails

  # Database settings
  db_host     = var.db_host
  db_port     = "3306"
  db_database = var.db_database
  db_user     = var.db_user
  db_password = var.db_password

  # DataOne settings
  dataone_s3_bucket     = var.dataone_s3_bucket
  dataone_s3_key_prefix = "dataone/exports"
}

# Main DescribedVehicleExtract task (weekly schedule, enabled for UAT testing)
module "described-vehicle-extract-dotnet" {
  source = "../../../modules/dotnet-processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_DescribedVehicleExtract_DotNet"
  container_definition_path = "../../../modules/dotnet-processing-apps/task-definitions/container-definitions/US_DescribedVehicleExtract_DotNet.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/describevehicleextract_dotnet_${var.environment}:latest"
  ssm_parameter_prefix      = module.ssm-parameters.ssm_parameter_prefix
  retention_in_days         = 7
  schedule_expression       = "0 02 ? * 1 *" # Run every Monday at 2:00am for UAT testing
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component
  cluster_arn   = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled       = var.enable_scheduled_tasks
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

# DataOne DescribedVehicleExtract task (daily schedule for UAT testing)
module "dataone-described-vehicle-extract-dotnet" {
  source = "../../../modules/dotnet-processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_DataOneDescribedVehicleExtract_DotNet"
  container_definition_path = "../../../modules/dotnet-processing-apps/task-definitions/container-definitions/US_DataOneDescribedVehicleExtract_DotNet.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/describevehicleextract_dotnet_${var.environment}:latest"
  ssm_parameter_prefix      = module.ssm-parameters.ssm_parameter_prefix
  retention_in_days         = 7
  schedule_expression       = "30 08 * * ? *" # Run at 8:30am every day for UAT testing
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component
  cluster_arn   = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled       = var.enable_scheduled_tasks
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}
