# Example terraform.tfvars for production environment
# Copy this file to terraform.tfvars and update with your actual values

# Basic Configuration
application = "ais"
service     = "processing-apps"
region      = "us-east-1"
environment = "production"
component   = "processing-apps"

# Deployment Metadata
build_number  = "1.0.0"
launched_by   = "deployment-pipeline"
launched_on   = "2024-01-15"
slack_contact = "#ais-engineering"

# AWS Account Configuration
account_id = "************"  # Replace with your AWS account ID

# Region Abbreviations Map
regions_abbreviated = {
  "us-east-1" = "ue1"
  "us-west-2" = "uw2"
}

# Component ID for tagging
processing_apps_component_id = "your-component-id"

# Database Configuration
# Update these values with your actual database settings
db_host     = "your-production-db-host.amazonaws.com"
db_database = "digirs_live_dump_us"
db_user     = "your-db-username"
db_password = "your-secure-db-password"  # Consider using AWS Secrets Manager

# Email Configuration
# SMTP credentials for sending notifications
email_smtp_user     = "AKIAIOSFODNN7EXAMPLE"  # Replace with your SMTP username
email_smtp_password = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"  # Replace with your SMTP password

# Email recipients for notifications
email_to_emails = [
  "<EMAIL>",
  "<EMAIL>"
]

# DataOne Configuration
# S3 bucket for DataOne file uploads
dataone_s3_bucket = "your-dataone-s3-bucket"

# Additional Configuration Notes:
# 
# 1. Database Configuration:
#    - Ensure the database host is accessible from your ECS cluster
#    - Use RDS endpoint if using AWS RDS
#    - Consider using AWS Secrets Manager for database credentials
#
# 2. Email Configuration:
#    - Use AWS SES SMTP credentials for reliable email delivery
#    - Ensure the SMTP user has permission to send emails
#    - Test email configuration in non-production environments first
#
# 3. S3 Configuration:
#    - Ensure the S3 bucket exists and is accessible
#    - Configure appropriate bucket policies for DataOne uploads
#    - Consider enabling versioning and lifecycle policies
#
# 4. Security Best Practices:
#    - Store sensitive values in AWS Secrets Manager or SSM Parameter Store
#    - Use IAM roles instead of hardcoded credentials where possible
#    - Regularly rotate passwords and access keys
#
# 5. Monitoring:
#    - Ensure CloudWatch logs retention is set appropriately
#    - Configure SNS topics for alarm notifications
#    - Set up dashboards for monitoring application performance
