# Outputs for production environment .NET Core task definitions

output "described_vehicle_extract_task_arn" {
  description = "ARN of the main DescribedVehicleExtract task definition"
  value       = module.described-vehicle-extract-dotnet.task_arn
}

output "dataone_described_vehicle_extract_task_arn" {
  description = "ARN of the DataOne DescribedVehicleExtract task definition"
  value       = module.dataone-described-vehicle-extract-dotnet.task_arn
}

output "log_group_names" {
  description = "Names of the CloudWatch log groups"
  value = {
    described_vehicle_extract         = module.described-vehicle-extract-dotnet.log_group_name
    dataone_described_vehicle_extract = module.dataone-described-vehicle-extract-dotnet.log_group_name
  }
}

output "scheduled_event_rule_arns" {
  description = "ARNs of the CloudWatch Events rules"
  value = {
    described_vehicle_extract         = module.described-vehicle-extract-dotnet.scheduled_event_rule_arn
    dataone_described_vehicle_extract = module.dataone-described-vehicle-extract-dotnet.scheduled_event_rule_arn
  }
}

output "ssm_parameter_prefix" {
  description = "SSM parameter prefix used for configuration"
  value       = module.ssm-parameters.ssm_parameter_prefix
}
