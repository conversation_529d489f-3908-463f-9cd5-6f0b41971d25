# Deployment Guide for .NET Core DescribedVehicleExtract

This guide provides step-by-step instructions for deploying the modernized .NET Core DescribedVehicleExtract application infrastructure.

## Prerequisites

1. **AWS CLI** configured with appropriate permissions
2. **Terraform** v1.0+ installed
3. **Docker** for building container images
4. **Access to ECR repository** for pushing images
5. **Existing ECS cluster** (reuses current infrastructure)

## Infrastructure Overview

### Key Changes from PHP to .NET Core

| Component | PHP (Legacy) | .NET Core (Modernized) |
|-----------|--------------|-------------------------|
| **Base Image** | Amazon Linux 1 + PHP 5.5 | Microsoft .NET 8.0 Runtime |
| **Configuration** | INI files from S3 | SSM Parameter Store |
| **Security** | Root user execution | Non-root user (1000:1000) |
| **Resource Usage** | 13312 CPU / 56320 MB | 2048 CPU / 4096 MB |
| **Health Checks** | None | Built-in .NET health checks |
| **Logging** | PHP error logs | Structured Serilog output |

## Deployment Steps

### Phase 1: Build and Push Docker Image

```bash
# Navigate to the application directory
cd src/VehicleExportWorkerService

# Build the Docker image
docker build -t describevehicleextract-dotnet:latest \
  --build-arg ARTIFACTORY_USERNAME=$ARTIFACTORY_USERNAME \
  --build-arg ARTIFACTORY_API_KEY=$ARTIFACTORY_API_KEY \
  .

# Tag for ECR
docker tag describevehicleextract-dotnet:latest \
  $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/describevehicleextract_dotnet_production:latest

# Push to ECR
aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com
docker push $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/describevehicleextract_dotnet_production:latest
```

### Phase 2: Deploy SSM Parameters

```bash
# Navigate to the modernized infrastructure
cd terraform-modernized/environments/production/task-definitions

# Initialize Terraform
terraform init

# Plan the deployment (SSM parameters first)
terraform plan -target=module.ssm-parameters

# Apply SSM parameters
terraform apply -target=module.ssm-parameters
```

### Phase 3: Deploy Task Definitions

```bash
# Deploy the main task definition
terraform plan -target=module.described-vehicle-extract-dotnet
terraform apply -target=module.described-vehicle-extract-dotnet

# Deploy the DataOne task definition
terraform plan -target=module.dataone-described-vehicle-extract-dotnet
terraform apply -target=module.dataone-described-vehicle-extract-dotnet
```

### Phase 4: Validation and Testing

```bash
# Check task definitions
aws ecs describe-task-definition --task-definition US_DescribedVehicleExtract_DotNet

# Test manual execution
aws ecs run-task \
  --cluster $CLUSTER_ARN \
  --task-definition US_DescribedVehicleExtract_DotNet \
  --launch-type EC2

# Monitor logs
aws logs tail US_DescribedVehicleExtract_DotNet_production --follow
```

## Configuration Management

### SSM Parameters Structure

```
/ais/describevehicleextract/production/
├── AppSettings/
│   ├── ActiveDescVehSaveDirectory
│   ├── EmailSettings/
│   │   ├── FromEmail
│   │   ├── SmtpServer
│   │   ├── SmtpPort
│   │   ├── SmtpUser
│   │   ├── SmtpPassword
│   │   ├── ToEmails/0
│   │   └── ToEmails/1
│   └── DataOneSettings/
│       ├── S3Bucket
│       └── S3KeyPrefix
└── DbConnectionInfo/
    ├── Host
    ├── Port
    ├── Database
    ├── User
    └── Password
```

### Environment-Specific Values

Update the following variables in `terraform.tfvars`:

```hcl
# Database Configuration
db_host     = "your-production-db-host"
db_database = "digirs_live_dump_us"
db_user     = "your-db-user"
db_password = "your-secure-password"

# Email Configuration
email_smtp_user     = "your-smtp-user"
email_smtp_password = "your-smtp-password"
email_to_emails     = ["<EMAIL>", "<EMAIL>"]

# DataOne Configuration
dataone_s3_bucket = "your-dataone-s3-bucket"
```

## Monitoring and Alerting

### CloudWatch Alarms

The modernized infrastructure includes enhanced monitoring:

1. **General Error Alarm**: Triggers on any ERROR log entries
2. **.NET Exception Alarm**: Specifically monitors for .NET exceptions
3. **Task Failure Alarm**: Monitors ECS task failures

### Log Analysis

Structured logging with Serilog provides better observability:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "Information",
  "logger": "VehicleExportWorkerService.Worker",
  "message": "Worker running at: 01/15/2024 10:30:00 +00:00",
  "properties": {
    "Environment": "Production",
    "TaskName": "US_DescribedVehicleExtract_DotNet"
  }
}
```

## Rollback Strategy

### Emergency Rollback

If issues occur, you can quickly revert to the PHP-based tasks:

```bash
# Disable .NET Core tasks
aws events disable-rule --name US_DescribedVehicleExtract_DotNet_production_schedule
aws events disable-rule --name US_DataOneDescribedVehicleExtract_DotNet_production_schedule

# Re-enable PHP tasks
aws events enable-rule --name US_DescribedVehicleExtract_production_schedule
aws events enable-rule --name US_DataOneDescribedVehicleExtract_production_schedule
```

### Gradual Migration

For a safer migration approach:

1. Deploy .NET Core tasks with `enabled = false`
2. Test manually using `aws ecs run-task`
3. Run both PHP and .NET Core tasks in parallel
4. Compare outputs and performance
5. Gradually shift traffic to .NET Core tasks
6. Decommission PHP tasks after validation

## Performance Optimization

### Resource Allocation

The .NET Core version uses significantly fewer resources:

- **CPU**: Reduced from 13312 to 2048 units (84% reduction)
- **Memory**: Reduced from 56320MB to 4096MB (93% reduction)

### Scaling Considerations

- Monitor CPU and memory usage during initial deployments
- Adjust resource allocation based on actual usage patterns
- Consider implementing auto-scaling if needed

## Security Enhancements

1. **Non-root execution**: Application runs as user 1000:1000
2. **Secrets management**: Sensitive data stored in SSM Parameter Store
3. **Network isolation**: Maintains existing VPC and security group configurations
4. **Image scanning**: Enable ECR vulnerability scanning

## Troubleshooting

### Common Issues

1. **SSM Parameter Access**: Ensure ECS task role has SSM permissions
2. **Database Connectivity**: Verify security groups allow MySQL access
3. **Image Pull Errors**: Check ECR permissions and image tags
4. **Configuration Issues**: Validate SSM parameter values

### Debug Commands

```bash
# Check task status
aws ecs describe-tasks --cluster $CLUSTER_ARN --tasks $TASK_ARN

# View container logs
aws logs get-log-events --log-group-name US_DescribedVehicleExtract_DotNet_production

# Validate SSM parameters
aws ssm get-parameters-by-path --path "/ais/describevehicleextract/production" --recursive
```
