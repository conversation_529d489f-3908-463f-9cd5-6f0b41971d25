# Modernized Terraform Infrastructure for .NET Core DescribedVehicleExtract

This directory contains the modernized Terraform infrastructure for the DescribedVehicleExtract application that has been migrated from PHP to .NET Core 8.0.

## Overview

The modernized infrastructure includes:

1. **Updated Container Definitions**: .NET Core-specific container definitions replacing PHP-based configurations
2. **SSM Parameter Store Integration**: Environment-specific configuration management
3. **Optimized Docker Images**: .NET Core runtime images instead of PHP base images
4. **Simplified Deployment**: Removal of INI package dependencies and PHP-specific scripts
5. **Enhanced Security**: Modern security practices and minimal container footprint

## Directory Structure

```
terraform-modernized/
├── modules/
│   ├── dotnet-processing-apps/
│   │   ├── task-definitions/
│   │   │   ├── container-definitions/
│   │   │   │   ├── US_DescribedVehicleExtract_DotNet.json
│   │   │   │   └── US_DataOneDescribedVehicleExtract_DotNet.json
│   │   │   ├── main.tf
│   │   │   ├── variables.tf
│   │   │   └── outputs.tf
│   │   └── ssm-parameters/
│   │       ├── main.tf
│   │       ├── variables.tf
│   │       └── outputs.tf
│   └── ecs-infrastructure/
│       ├── cluster/
│       ├── security-groups/
│       └── load-balancers/
├── environments/
│   ├── production/
│   ├── staging/
│   ├── uat/
│   └── development/
└── shared-variables/
```

## Key Changes from Legacy Infrastructure

### Container Definitions
- **Before**: PHP 5.5 with complex shell commands and INI package downloads
- **After**: .NET Core 8.0 runtime with direct application execution

### Configuration Management
- **Before**: INI files downloaded from S3 and extracted at runtime
- **After**: SSM Parameter Store with environment-specific parameters injected as environment variables

### Base Images
- **Before**: Amazon Linux 1 with PHP 5.5, Apache, and numerous PHP extensions
- **After**: Microsoft .NET Core 8.0 runtime image (mcr.microsoft.com/dotnet/aspnet:8.0)

### Security
- **Before**: Root user execution with sudo commands
- **After**: Non-root user execution with minimal privileges

## Deployment Process

1. **Build Phase**: Docker image built from .NET Core application
2. **Configuration Phase**: SSM parameters populated for target environment
3. **Deployment Phase**: ECS task definition updated with new image and configuration
4. **Execution Phase**: Scheduled task runs .NET Core application directly

## Compatibility

This modernized infrastructure is designed to:
- Work with existing ECS clusters without modification
- Maintain the same scheduling patterns as legacy tasks
- Preserve existing CloudWatch logging and monitoring
- Support the same environment promotion workflow

## Migration Strategy

The migration can be performed environment by environment:
1. Deploy modernized infrastructure alongside existing
2. Test .NET Core tasks in parallel with PHP tasks
3. Switch traffic/scheduling to .NET Core tasks
4. Decommission PHP-based tasks after validation
