#!/bin/bash

# Deployment Validation Script for .NET Core DescribedVehicleExtract
# This script validates the deployment of the modernized infrastructure

set -e

# Configuration
ENVIRONMENT=${1:-"staging"}
REGION=${2:-"us-east-1"}
CLUSTER_NAME="ais-${ENVIRONMENT}-processing-apps-ecs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validation functions
validate_prerequisites() {
    log_info "Validating prerequisites..."
    
    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed"
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials not configured"
        exit 1
    fi
    
    # Check Terraform
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform is not installed"
        exit 1
    fi
    
    log_info "Prerequisites validated successfully"
}

validate_ssm_parameters() {
    log_info "Validating SSM parameters..."
    
    local ssm_prefix="/ais/describevehicleextract/${ENVIRONMENT}"
    local required_params=(
        "${ssm_prefix}/AppSettings/ActiveDescVehSaveDirectory"
        "${ssm_prefix}/AppSettings/EmailSettings/FromEmail"
        "${ssm_prefix}/DbConnectionInfo/Host"
        "${ssm_prefix}/DbConnectionInfo/Database"
    )
    
    for param in "${required_params[@]}"; do
        if aws ssm get-parameter --name "$param" --region "$REGION" &> /dev/null; then
            log_info "✓ Parameter exists: $param"
        else
            log_error "✗ Missing parameter: $param"
            exit 1
        fi
    done
    
    log_info "SSM parameters validated successfully"
}

validate_task_definitions() {
    log_info "Validating ECS task definitions..."
    
    local task_definitions=(
        "US_DescribedVehicleExtract_DotNet"
        "US_DataOneDescribedVehicleExtract_DotNet"
    )
    
    for task_def in "${task_definitions[@]}"; do
        if aws ecs describe-task-definition --task-definition "$task_def" --region "$REGION" &> /dev/null; then
            log_info "✓ Task definition exists: $task_def"
            
            # Check task definition details
            local cpu=$(aws ecs describe-task-definition --task-definition "$task_def" --region "$REGION" --query 'taskDefinition.cpu' --output text)
            local memory=$(aws ecs describe-task-definition --task-definition "$task_def" --region "$REGION" --query 'taskDefinition.memory' --output text)
            
            log_info "  CPU: $cpu, Memory: $memory"
        else
            log_error "✗ Missing task definition: $task_def"
            exit 1
        fi
    done
    
    log_info "Task definitions validated successfully"
}

validate_log_groups() {
    log_info "Validating CloudWatch log groups..."
    
    local log_groups=(
        "US_DescribedVehicleExtract_DotNet_${ENVIRONMENT}"
        "US_DataOneDescribedVehicleExtract_DotNet_${ENVIRONMENT}"
    )
    
    for log_group in "${log_groups[@]}"; do
        if aws logs describe-log-groups --log-group-name-prefix "$log_group" --region "$REGION" --query 'logGroups[0].logGroupName' --output text | grep -q "$log_group"; then
            log_info "✓ Log group exists: $log_group"
        else
            log_error "✗ Missing log group: $log_group"
            exit 1
        fi
    done
    
    log_info "Log groups validated successfully"
}

validate_scheduled_events() {
    log_info "Validating CloudWatch Events rules..."
    
    local event_rules=(
        "US_DescribedVehicleExtract_DotNet_${ENVIRONMENT}_schedule"
        "US_DataOneDescribedVehicleExtract_DotNet_${ENVIRONMENT}_schedule"
    )
    
    for rule in "${event_rules[@]}"; do
        if aws events describe-rule --name "$rule" --region "$REGION" &> /dev/null; then
            local state=$(aws events describe-rule --name "$rule" --region "$REGION" --query 'State' --output text)
            log_info "✓ Event rule exists: $rule (State: $state)"
        else
            log_warn "⚠ Event rule not found: $rule (may be intentional for some environments)"
        fi
    done
    
    log_info "Scheduled events validation completed"
}

validate_ecr_images() {
    log_info "Validating ECR images..."
    
    local repository_name="describevehicleextract_dotnet_${ENVIRONMENT}"
    
    if aws ecr describe-repositories --repository-names "$repository_name" --region "$REGION" &> /dev/null; then
        log_info "✓ ECR repository exists: $repository_name"
        
        # Check for images
        local image_count=$(aws ecr list-images --repository-name "$repository_name" --region "$REGION" --query 'length(imageIds)')
        if [ "$image_count" -gt 0 ]; then
            log_info "  Images available: $image_count"
        else
            log_warn "  No images found in repository"
        fi
    else
        log_error "✗ Missing ECR repository: $repository_name"
        exit 1
    fi
    
    log_info "ECR images validated successfully"
}

test_manual_execution() {
    log_info "Testing manual task execution..."
    
    local task_def="US_DescribedVehicleExtract_DotNet"
    
    log_info "Starting task: $task_def"
    local task_arn=$(aws ecs run-task \
        --cluster "$CLUSTER_NAME" \
        --task-definition "$task_def" \
        --launch-type EC2 \
        --region "$REGION" \
        --query 'tasks[0].taskArn' \
        --output text)
    
    if [ "$task_arn" != "None" ] && [ -n "$task_arn" ]; then
        log_info "✓ Task started successfully: $task_arn"
        
        # Wait for task to start
        log_info "Waiting for task to start..."
        aws ecs wait tasks-running --cluster "$CLUSTER_NAME" --tasks "$task_arn" --region "$REGION"
        
        # Get task status
        local task_status=$(aws ecs describe-tasks \
            --cluster "$CLUSTER_NAME" \
            --tasks "$task_arn" \
            --region "$REGION" \
            --query 'tasks[0].lastStatus' \
            --output text)
        
        log_info "Task status: $task_status"
        
        # Stop the test task
        log_info "Stopping test task..."
        aws ecs stop-task --cluster "$CLUSTER_NAME" --task "$task_arn" --region "$REGION" &> /dev/null
        
        log_info "Manual execution test completed successfully"
    else
        log_error "✗ Failed to start task"
        exit 1
    fi
}

validate_alarms() {
    log_info "Validating CloudWatch alarms..."
    
    local alarm_prefix="TASK_US_DescribedVehicleExtract_DotNet_${ENVIRONMENT}"
    
    local alarms=$(aws cloudwatch describe-alarms \
        --alarm-name-prefix "$alarm_prefix" \
        --region "$REGION" \
        --query 'MetricAlarms[].AlarmName' \
        --output text)
    
    if [ -n "$alarms" ]; then
        log_info "✓ CloudWatch alarms found:"
        for alarm in $alarms; do
            log_info "  - $alarm"
        done
    else
        log_warn "⚠ No CloudWatch alarms found with prefix: $alarm_prefix"
    fi
    
    log_info "Alarm validation completed"
}

generate_report() {
    log_info "Generating validation report..."
    
    local report_file="validation-report-${ENVIRONMENT}-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
Deployment Validation Report
============================
Environment: $ENVIRONMENT
Region: $REGION
Date: $(date)
Cluster: $CLUSTER_NAME

Validation Results:
- SSM Parameters: ✓ PASSED
- Task Definitions: ✓ PASSED
- Log Groups: ✓ PASSED
- Scheduled Events: ✓ CHECKED
- ECR Images: ✓ PASSED
- Manual Execution: ✓ PASSED
- CloudWatch Alarms: ✓ CHECKED

Next Steps:
1. Review any warnings in the output above
2. Test scheduled execution if enabled
3. Monitor logs for any issues
4. Validate output files if applicable

For manual testing, use:
aws ecs run-task --cluster $CLUSTER_NAME --task-definition US_DescribedVehicleExtract_DotNet --launch-type EC2

For log monitoring, use:
aws logs tail US_DescribedVehicleExtract_DotNet_${ENVIRONMENT} --follow
EOF
    
    log_info "Validation report saved to: $report_file"
}

# Main execution
main() {
    log_info "Starting deployment validation for environment: $ENVIRONMENT"
    log_info "Region: $REGION"
    log_info "Cluster: $CLUSTER_NAME"
    echo
    
    validate_prerequisites
    echo
    
    validate_ssm_parameters
    echo
    
    validate_task_definitions
    echo
    
    validate_log_groups
    echo
    
    validate_scheduled_events
    echo
    
    validate_ecr_images
    echo
    
    validate_alarms
    echo
    
    if [ "$ENVIRONMENT" != "production" ]; then
        test_manual_execution
        echo
    else
        log_info "Skipping manual execution test in production environment"
        echo
    fi
    
    generate_report
    echo
    
    log_info "Deployment validation completed successfully!"
    log_info "Review the validation report and any warnings above."
}

# Script usage
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [environment] [region]"
    echo "  environment: development, staging, uat, production (default: staging)"
    echo "  region: AWS region (default: us-east-1)"
    echo
    echo "Examples:"
    echo "  $0 staging us-east-1"
    echo "  $0 production us-west-2"
    exit 0
fi

# Run main function
main
