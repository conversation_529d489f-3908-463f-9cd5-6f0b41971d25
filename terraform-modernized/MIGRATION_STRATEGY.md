# Migration Strategy: PHP to .NET Core DescribedVehicleExtract

This document outlines the comprehensive migration strategy for transitioning the DescribedVehicleExtract application from PHP to .NET Core while maintaining system reliability and minimizing downtime.

## Migration Overview

### Objectives

1. **Zero-Downtime Migration**: Ensure continuous operation during transition
2. **Risk Mitigation**: Minimize impact on production systems
3. **Validation**: Comprehensive testing at each stage
4. **Rollback Capability**: Quick reversion if issues arise
5. **Performance Improvement**: Achieve better resource utilization

### Migration Phases

```
Phase 1: Infrastructure Preparation (Week 1-2)
Phase 2: Development Environment (Week 3)
Phase 3: Staging Environment (Week 4)
Phase 4: UAT Environment (Week 5-6)
Phase 5: Production Deployment (Week 7-8)
Phase 6: Monitoring & Optimization (Week 9-10)
Phase 7: Legacy Cleanup (Week 11-12)
```

## Phase 1: Infrastructure Preparation

### Week 1: Foundation Setup

#### Day 1-2: ECR Repository Setup
```bash
# Create ECR repository for .NET Core images
aws ecr create-repository --repository-name describevehicleextract_dotnet_development
aws ecr create-repository --repository-name describevehicleextract_dotnet_staging
aws ecr create-repository --repository-name describevehicleextract_dotnet_uat
aws ecr create-repository --repository-name describevehicleextract_dotnet_production

# Configure lifecycle policies
aws ecr put-lifecycle-policy --repository-name describevehicleextract_dotnet_production \
  --lifecycle-policy-text file://ecr-lifecycle-policy.json
```

#### Day 3-4: SSM Parameter Store Setup
```bash
# Deploy SSM parameters for all environments
cd terraform-modernized/environments/development/task-definitions
terraform init && terraform apply -target=module.ssm-parameters

cd ../staging/task-definitions
terraform init && terraform apply -target=module.ssm-parameters

cd ../uat/task-definitions
terraform init && terraform apply -target=module.ssm-parameters

cd ../production/task-definitions
terraform init && terraform apply -target=module.ssm-parameters
```

#### Day 5: Security & IAM Setup
- Validate ECS task roles have SSM parameter access
- Configure ECR permissions for image pulls
- Review security group configurations

### Week 2: Build Pipeline Setup

#### Day 1-3: CI/CD Pipeline Configuration
```yaml
# Example GitHub Actions workflow
name: Build and Deploy .NET Core DescribedVehicleExtract
on:
  push:
    branches: [main, develop]
    paths: ['src/VehicleExportWorkerService/**']

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.x'
      
      - name: Build and Test
        run: |
          cd src/VehicleExportWorkerService
          dotnet restore
          dotnet build --configuration Release
          dotnet test
      
      - name: Build Docker Image
        run: |
          docker build -t describevehicleextract-dotnet:${{ github.sha }} \
            --build-arg ARTIFACTORY_USERNAME=${{ secrets.ARTIFACTORY_USERNAME }} \
            --build-arg ARTIFACTORY_API_KEY=${{ secrets.ARTIFACTORY_API_KEY }} \
            src/VehicleExportWorkerService
      
      - name: Push to ECR
        run: |
          aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com
          docker tag describevehicleextract-dotnet:${{ github.sha }} ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com/describevehicleextract_dotnet_development:${{ github.sha }}
          docker push ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com/describevehicleextract_dotnet_development:${{ github.sha }}
```

#### Day 4-5: Testing Framework Setup
- Unit test execution in pipeline
- Integration test configuration
- Performance baseline establishment

## Phase 2: Development Environment

### Week 3: Development Deployment & Testing

#### Day 1-2: Initial Deployment
```bash
# Deploy to development environment
cd terraform-modernized/environments/development/task-definitions
terraform plan
terraform apply

# Verify deployment
aws ecs describe-task-definition --task-definition US_DescribedVehicleExtract_DotNet
```

#### Day 3-4: Functional Testing
```bash
# Manual task execution
aws ecs run-task \
  --cluster ais-development-processing-apps-ecs \
  --task-definition US_DescribedVehicleExtract_DotNet \
  --launch-type EC2

# Monitor execution
aws logs tail US_DescribedVehicleExtract_DotNet_development --follow
```

#### Day 5: Performance Baseline
- Measure execution time
- Monitor resource usage
- Compare with PHP baseline
- Document performance metrics

## Phase 3: Staging Environment

### Week 4: Staging Deployment & Validation

#### Day 1-2: Staging Deployment
```bash
# Deploy to staging environment
cd terraform-modernized/environments/staging/task-definitions
terraform plan
terraform apply

# Validate configuration
aws ssm get-parameters-by-path --path "/ais/describevehicleextract/staging" --recursive
```

#### Day 3-4: Parallel Execution Testing
```bash
# Run both PHP and .NET Core versions
# PHP version (existing)
aws ecs run-task \
  --cluster ais-staging-processing-apps-ecs \
  --task-definition US_DescribedVehicleExtract \
  --launch-type EC2

# .NET Core version (new)
aws ecs run-task \
  --cluster ais-staging-processing-apps-ecs \
  --task-definition US_DescribedVehicleExtract_DotNet \
  --launch-type EC2
```

#### Day 5: Output Validation
- Compare CSV outputs between PHP and .NET Core versions
- Validate data integrity
- Check file formats and content
- Performance comparison

## Phase 4: UAT Environment

### Week 5-6: User Acceptance Testing

#### Week 5: UAT Deployment
```bash
# Deploy to UAT environment with scheduled execution
cd terraform-modernized/environments/uat/task-definitions
terraform plan -var="enable_scheduled_tasks=true"
terraform apply -var="enable_scheduled_tasks=true"
```

#### Week 6: Comprehensive Testing
- **Functional Testing**: Verify all features work correctly
- **Integration Testing**: Ensure proper database connectivity
- **Email Testing**: Validate notification system
- **Error Handling**: Test failure scenarios
- **Performance Testing**: Load and stress testing

### UAT Test Scenarios

1. **Normal Execution**
   - Scheduled task execution
   - Manual task execution
   - Output file generation
   - Email notifications

2. **Error Scenarios**
   - Database connectivity issues
   - Invalid configuration
   - Insufficient permissions
   - Resource constraints

3. **Performance Tests**
   - Large dataset processing
   - Concurrent execution
   - Resource utilization monitoring

## Phase 5: Production Deployment

### Week 7: Production Preparation

#### Day 1-3: Pre-Production Checklist
- [ ] All UAT tests passed
- [ ] Performance benchmarks met
- [ ] Security review completed
- [ ] Rollback procedures tested
- [ ] Monitoring dashboards configured
- [ ] Alert thresholds set

#### Day 4-5: Production Deployment
```bash
# Deploy .NET Core tasks (disabled initially)
cd terraform-modernized/environments/production/task-definitions
terraform plan
terraform apply

# Verify deployment without enabling schedules
aws ecs describe-task-definition --task-definition US_DescribedVehicleExtract_DotNet
```

### Week 8: Gradual Migration

#### Day 1-2: Shadow Mode
```bash
# Run .NET Core tasks manually alongside scheduled PHP tasks
aws ecs run-task \
  --cluster ais-production-processing-apps-ecs \
  --task-definition US_DescribedVehicleExtract_DotNet \
  --launch-type EC2

# Compare outputs
diff php_output.csv dotnet_output.csv
```

#### Day 3-4: Parallel Execution
```bash
# Enable .NET Core schedules with different timing
aws events put-rule \
  --name US_DescribedVehicleExtract_DotNet_production_schedule \
  --schedule-expression "cron(0 02 ? * 7 *)"  # 2 hours after PHP version

aws events enable-rule --name US_DescribedVehicleExtract_DotNet_production_schedule
```

#### Day 5: Full Migration
```bash
# Disable PHP tasks
aws events disable-rule --name US_DescribedVehicleExtract_production_schedule
aws events disable-rule --name US_DataOneDescribedVehicleExtract_production_schedule

# Update .NET Core schedules to production timing
aws events put-rule \
  --name US_DescribedVehicleExtract_DotNet_production_schedule \
  --schedule-expression "cron(45 01 ? * 7 *)"  # Original production schedule
```

## Phase 6: Monitoring & Optimization

### Week 9-10: Post-Migration Monitoring

#### Monitoring Checklist
- [ ] Task execution success rates
- [ ] Performance metrics (CPU, memory, execution time)
- [ ] Error rates and types
- [ ] Output file quality and consistency
- [ ] Email delivery success
- [ ] Cost optimization opportunities

#### Performance Optimization
```bash
# Monitor resource usage
aws cloudwatch get-metric-statistics \
  --namespace AWS/ECS \
  --metric-name CPUUtilization \
  --dimensions Name=ServiceName,Value=US_DescribedVehicleExtract_DotNet \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-08T00:00:00Z \
  --period 3600 \
  --statistics Average,Maximum

# Adjust resource allocation if needed
terraform apply -var="cpu=1024" -var="memory=2048"
```

## Phase 7: Legacy Cleanup

### Week 11-12: Decommissioning PHP Infrastructure

#### Week 11: Validation Period
- Confirm .NET Core version stability
- Validate all functionality
- Ensure no regressions
- Get stakeholder approval for cleanup

#### Week 12: Legacy Removal
```bash
# Remove PHP task definitions
terraform destroy -target=module.described-vehicle-extract
terraform destroy -target=module.dataone-described-vehicle-extract

# Clean up unused resources
aws ecr delete-repository --repository-name processingapps_production --force
aws s3 rm s3://ini-bucket/production/ProcessingApps/ --recursive
```

## Risk Mitigation Strategies

### Rollback Procedures

#### Immediate Rollback (< 5 minutes)
```bash
# Disable .NET Core tasks
aws events disable-rule --name US_DescribedVehicleExtract_DotNet_production_schedule
aws events disable-rule --name US_DataOneDescribedVehicleExtract_DotNet_production_schedule

# Re-enable PHP tasks
aws events enable-rule --name US_DescribedVehicleExtract_production_schedule
aws events enable-rule --name US_DataOneDescribedVehicleExtract_production_schedule
```

#### Full Rollback (< 30 minutes)
```bash
# Revert to previous task definitions
aws ecs update-service \
  --cluster ais-production-processing-apps-ecs \
  --service described-vehicle-extract \
  --task-definition US_DescribedVehicleExtract:previous-revision
```

### Monitoring & Alerting

#### Key Metrics to Monitor
1. **Task Success Rate**: > 99%
2. **Execution Time**: Within 10% of baseline
3. **Resource Utilization**: CPU < 80%, Memory < 80%
4. **Error Rate**: < 1%
5. **Output File Size**: Within expected range

#### Alert Thresholds
```yaml
alerts:
  - name: "Task Failure Rate"
    threshold: "> 5% in 1 hour"
    action: "Page on-call engineer"
  
  - name: "Performance Degradation"
    threshold: "> 50% increase in execution time"
    action: "Send warning notification"
  
  - name: "Resource Exhaustion"
    threshold: "CPU > 90% or Memory > 90%"
    action: "Auto-scale or alert"
```

## Success Criteria

### Technical Success Metrics
- [ ] 100% functional parity with PHP version
- [ ] < 5% performance variance from baseline
- [ ] Zero data loss or corruption
- [ ] All scheduled tasks executing successfully
- [ ] Monitoring and alerting operational

### Business Success Metrics
- [ ] No impact on downstream systems
- [ ] Stakeholder approval of outputs
- [ ] Cost reduction achieved (target: 60-70%)
- [ ] Improved system reliability
- [ ] Enhanced security posture

## Communication Plan

### Stakeholder Updates
- **Weekly Status Reports**: Every Friday during migration
- **Go/No-Go Meetings**: Before each phase
- **Incident Communications**: Real-time during issues
- **Post-Migration Review**: Within 2 weeks of completion

### Documentation Updates
- [ ] Update operational runbooks
- [ ] Revise monitoring procedures
- [ ] Update disaster recovery plans
- [ ] Create troubleshooting guides

This migration strategy ensures a systematic, low-risk transition from PHP to .NET Core while maintaining system reliability and providing clear rollback options at every stage.
