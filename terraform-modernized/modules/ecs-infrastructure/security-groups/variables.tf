# Variables for Security Groups

variable "application" {
  description = "Application name"
  type        = string
}

variable "service" {
  description = "Service name"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "component" {
  description = "Component name"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where security groups will be created"
  type        = string
}

variable "vpc_cidr" {
  description = "VPC CIDR block"
  type        = string
}

variable "database_cidrs" {
  description = "List of CIDR blocks for database access"
  type        = list(string)
  default     = []
}

variable "enable_efs_access" {
  description = "Enable EFS access security group"
  type        = bool
  default     = false
}

variable "enable_health_check_access" {
  description = "Enable health check access from load balancers"
  type        = bool
  default     = false
}

variable "custom_egress_rules" {
  description = "Custom egress rules for applications"
  type = list(object({
    from_port   = number
    to_port     = number
    protocol    = string
    cidr_blocks = list(string)
    description = string
  }))
  default = []
}

variable "custom_ingress_rules" {
  description = "Custom ingress rules for applications"
  type = list(object({
    from_port   = number
    to_port     = number
    protocol    = string
    cidr_blocks = list(string)
    description = string
  }))
  default = []
}
