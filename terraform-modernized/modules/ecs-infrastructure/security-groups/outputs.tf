# Outputs for Security Groups

output "ecs_tasks_security_group_id" {
  description = "ID of the ECS tasks security group"
  value       = aws_security_group.ecs_tasks.id
}

output "database_access_security_group_id" {
  description = "ID of the database access security group"
  value       = aws_security_group.database_access.id
}

output "aws_services_security_group_id" {
  description = "ID of the AWS services security group"
  value       = aws_security_group.aws_services.id
}

output "email_access_security_group_id" {
  description = "ID of the email access security group"
  value       = aws_security_group.email_access.id
}

output "efs_access_security_group_id" {
  description = "ID of the EFS access security group"
  value       = var.enable_efs_access ? aws_security_group.efs_access[0].id : null
}

output "dotnet_application_security_group_id" {
  description = "ID of the composite .NET application security group"
  value       = aws_security_group.dotnet_application.id
}

output "all_security_group_ids" {
  description = "List of all security group IDs"
  value = compact([
    aws_security_group.ecs_tasks.id,
    aws_security_group.database_access.id,
    aws_security_group.aws_services.id,
    aws_security_group.email_access.id,
    var.enable_efs_access ? aws_security_group.efs_access[0].id : null,
    aws_security_group.dotnet_application.id
  ])
}

output "security_group_arns" {
  description = "Map of security group names to ARNs"
  value = {
    ecs_tasks           = aws_security_group.ecs_tasks.arn
    database_access     = aws_security_group.database_access.arn
    aws_services        = aws_security_group.aws_services.arn
    email_access        = aws_security_group.email_access.arn
    efs_access          = var.enable_efs_access ? aws_security_group.efs_access[0].arn : null
    dotnet_application  = aws_security_group.dotnet_application.arn
  }
}
