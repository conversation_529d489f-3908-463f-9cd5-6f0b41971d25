# Security Groups for .NET Core processing applications

# Security Group for ECS Tasks
resource "aws_security_group" "ecs_tasks" {
  name        = "${var.application}-${var.environment}-dotnet-tasks"
  description = "Security group for .NET Core ECS tasks"
  vpc_id      = var.vpc_id

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "All outbound traffic"
  }

  tags = {
    Name        = "${var.application}-${var.environment}-dotnet-tasks"
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
    Purpose     = "ECS-Tasks"
  }
}

# Security Group for Database Access
resource "aws_security_group" "database_access" {
  name        = "${var.application}-${var.environment}-dotnet-db-access"
  description = "Security group for .NET Core applications to access databases"
  vpc_id      = var.vpc_id

  # MySQL/MariaDB access
  egress {
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = var.database_cidrs
    description = "MySQL/MariaDB access"
  }

  # PostgreSQL access (if needed)
  egress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = var.database_cidrs
    description = "PostgreSQL access"
  }

  tags = {
    Name        = "${var.application}-${var.environment}-dotnet-db-access"
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
    Purpose     = "Database-Access"
  }
}

# Security Group for S3 and AWS Services Access
resource "aws_security_group" "aws_services" {
  name        = "${var.application}-${var.environment}-dotnet-aws-services"
  description = "Security group for .NET Core applications to access AWS services"
  vpc_id      = var.vpc_id

  # HTTPS for AWS API calls
  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS for AWS API calls"
  }

  # HTTP for metadata service
  egress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["***************/32"]
    description = "EC2 metadata service"
  }

  tags = {
    Name        = "${var.application}-${var.environment}-dotnet-aws-services"
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
    Purpose     = "AWS-Services"
  }
}

# Security Group for Email/SMTP Access
resource "aws_security_group" "email_access" {
  name        = "${var.application}-${var.environment}-dotnet-email"
  description = "Security group for .NET Core applications to send emails"
  vpc_id      = var.vpc_id

  # SMTP access
  egress {
    from_port   = 25
    to_port     = 25
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "SMTP access"
  }

  # SMTP over TLS
  egress {
    from_port   = 587
    to_port     = 587
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "SMTP over TLS"
  }

  # SMTP over SSL
  egress {
    from_port   = 465
    to_port     = 465
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "SMTP over SSL"
  }

  tags = {
    Name        = "${var.application}-${var.environment}-dotnet-email"
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
    Purpose     = "Email-Access"
  }
}

# Security Group for EFS Access (if using shared storage)
resource "aws_security_group" "efs_access" {
  count = var.enable_efs_access ? 1 : 0

  name        = "${var.application}-${var.environment}-dotnet-efs"
  description = "Security group for .NET Core applications to access EFS"
  vpc_id      = var.vpc_id

  # NFS access
  egress {
    from_port   = 2049
    to_port     = 2049
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
    description = "NFS access to EFS"
  }

  tags = {
    Name        = "${var.application}-${var.environment}-dotnet-efs"
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
    Purpose     = "EFS-Access"
  }
}

# Composite Security Group that includes all necessary access
resource "aws_security_group" "dotnet_application" {
  name        = "${var.application}-${var.environment}-dotnet-app-composite"
  description = "Composite security group for .NET Core applications"
  vpc_id      = var.vpc_id

  # Allow all outbound traffic (simplified approach)
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "All outbound traffic"
  }

  # Allow inbound health checks from ALB (if needed)
  dynamic "ingress" {
    for_each = var.enable_health_check_access ? [1] : []
    content {
      from_port   = 80
      to_port     = 80
      protocol    = "tcp"
      cidr_blocks = [var.vpc_cidr]
      description = "Health check access"
    }
  }

  tags = {
    Name        = "${var.application}-${var.environment}-dotnet-app-composite"
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
    Purpose     = "Application-Composite"
  }
}
