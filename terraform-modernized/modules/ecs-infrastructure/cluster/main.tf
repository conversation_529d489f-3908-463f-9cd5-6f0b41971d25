# ECS Cluster infrastructure for .NET Core processing applications
# This module copies the essential ECS cluster components needed for the modernized deployment

# Create the ECS Cluster
resource "aws_ecs_cluster" "dotnet_processing" {
  name = "${var.application}-${var.environment}-${var.component}-dotnet-ecs"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  configuration {
    execute_command_configuration {
      logging = "OVERRIDE"
      
      log_configuration {
        cloud_watch_encryption_enabled = true
        cloud_watch_log_group_name     = aws_cloudwatch_log_group.ecs_cluster.name
      }
    }
  }

  tags = {
    Name        = "${var.application}-${var.environment}-${var.component}-dotnet-ecs"
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
    Runtime     = "DotNetCore"
  }

  lifecycle {
    create_before_destroy = true
  }
}

# CloudWatch Log Group for ECS Cluster
resource "aws_cloudwatch_log_group" "ecs_cluster" {
  name              = "/aws/ecs/cluster/${var.application}-${var.environment}-${var.component}-dotnet"
  retention_in_days = var.log_retention_days

  tags = {
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
  }
}

# Security Group for ECS instances
resource "aws_security_group" "ecs_instances" {
  name        = "${var.application}-${var.service}-${var.environment}-dotnet-processing-ec2"
  description = "Security group for .NET Core processing ECS instances"
  vpc_id      = var.vpc_id

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "All outbound traffic"
  }

  # Allow SSH from management networks
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.management_cidrs
    description = "SSH access from management networks"
  }

  # Allow ECS agent communication
  ingress {
    from_port   = 51678
    to_port     = 51679
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
    description = "ECS agent communication"
  }

  # Allow dynamic port range for ECS tasks
  ingress {
    from_port   = 32768
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
    description = "Dynamic port range for ECS tasks"
  }

  tags = {
    Name        = "${var.application}-${var.service}-${var.environment}-dotnet-processing-ec2"
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
  }

  lifecycle {
    create_before_destroy = true
  }
}

# ECS Capacity Provider for Auto Scaling
resource "aws_ecs_capacity_provider" "dotnet_processing" {
  name = "${var.application}-${var.environment}-dotnet-processing-cp"

  auto_scaling_group_provider {
    auto_scaling_group_arn         = var.auto_scaling_group_arn
    managed_termination_protection = "ENABLED"

    managed_scaling {
      maximum_scaling_step_size = 10
      minimum_scaling_step_size = 1
      status                    = "ENABLED"
      target_capacity           = 100
    }
  }

  tags = {
    Name        = "${var.application}-${var.environment}-dotnet-processing-cp"
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
  }
}

# Associate capacity provider with cluster
resource "aws_ecs_cluster_capacity_providers" "dotnet_processing" {
  cluster_name = aws_ecs_cluster.dotnet_processing.name

  capacity_providers = [aws_ecs_capacity_provider.dotnet_processing.name]

  default_capacity_provider_strategy {
    base              = 1
    weight            = 100
    capacity_provider = aws_ecs_capacity_provider.dotnet_processing.name
  }
}
