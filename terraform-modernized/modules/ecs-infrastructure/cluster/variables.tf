# Variables for ECS Cluster infrastructure

variable "application" {
  description = "Application name"
  type        = string
}

variable "service" {
  description = "Service name"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "component" {
  description = "Component name"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where the cluster will be created"
  type        = string
}

variable "vpc_cidr" {
  description = "VPC CIDR block"
  type        = string
}

variable "private_subnet_ids" {
  description = "List of private subnet IDs"
  type        = list(string)
}

variable "management_cidrs" {
  description = "List of CIDR blocks for management access"
  type        = list(string)
  default     = []
}

variable "auto_scaling_group_arn" {
  description = "ARN of the Auto Scaling Group for ECS instances"
  type        = string
  default     = ""
}

variable "log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 7
}

variable "enable_container_insights" {
  description = "Enable CloudWatch Container Insights"
  type        = bool
  default     = true
}

variable "enable_execute_command" {
  description = "Enable ECS Exec for debugging"
  type        = bool
  default     = true
}
