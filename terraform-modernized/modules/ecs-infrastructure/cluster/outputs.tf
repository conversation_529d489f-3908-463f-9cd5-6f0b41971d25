# Outputs for ECS Cluster infrastructure

output "cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = aws_ecs_cluster.dotnet_processing.arn
}

output "cluster_name" {
  description = "Name of the ECS cluster"
  value       = aws_ecs_cluster.dotnet_processing.name
}

output "cluster_id" {
  description = "ID of the ECS cluster"
  value       = aws_ecs_cluster.dotnet_processing.id
}

output "security_group_id" {
  description = "ID of the ECS instances security group"
  value       = aws_security_group.ecs_instances.id
}

output "security_group_arn" {
  description = "ARN of the ECS instances security group"
  value       = aws_security_group.ecs_instances.arn
}

output "capacity_provider_name" {
  description = "Name of the ECS capacity provider"
  value       = aws_ecs_capacity_provider.dotnet_processing.name
}

output "log_group_name" {
  description = "Name of the CloudWatch log group for the cluster"
  value       = aws_cloudwatch_log_group.ecs_cluster.name
}

output "log_group_arn" {
  description = "ARN of the CloudWatch log group for the cluster"
  value       = aws_cloudwatch_log_group.ecs_cluster.arn
}
