# Variables for SSM Parameter Store configuration

variable "ssm_parameter_prefix" {
  description = "Prefix for SSM parameter names"
  type        = string
  default     = "/ais/describevehicleextract"
}

variable "application" {
  description = "Application name"
  type        = string
}

variable "service" {
  description = "Service name"
  type        = string
}

variable "environment" {
  description = "Environment name (e.g., production, staging, uat)"
  type        = string
}

variable "component" {
  description = "Component name"
  type        = string
}

# Application Settings Variables
variable "active_desc_veh_save_directory" {
  description = "Directory path for saving Active Described Vehicle files"
  type        = string
  default     = "/app/data/exports"
}

# Email Settings Variables
variable "email_from_email" {
  description = "From email address for notifications"
  type        = string
  default     = "<EMAIL>"
}

variable "email_smtp_server" {
  description = "SMTP server for email notifications"
  type        = string
  default     = "email-smtp.us-east-1.amazonaws.com"
}

variable "email_smtp_port" {
  description = "SMTP port for email notifications"
  type        = string
  default     = "25"
}

variable "email_smtp_user" {
  description = "SMTP username for email notifications"
  type        = string
  sensitive   = true
}

variable "email_smtp_password" {
  description = "SMTP password for email notifications"
  type        = string
  sensitive   = true
}

variable "email_to_emails" {
  description = "List of recipient email addresses"
  type        = list(string)
  default     = ["<EMAIL>", "<EMAIL>"]
}

# Database Connection Variables
variable "db_host" {
  description = "Database host"
  type        = string
}

variable "db_port" {
  description = "Database port"
  type        = string
  default     = "3306"
}

variable "db_database" {
  description = "Database name"
  type        = string
}

variable "db_user" {
  description = "Database username"
  type        = string
  sensitive   = true
}

variable "db_password" {
  description = "Database password"
  type        = string
  sensitive   = true
}

# DataOne Settings Variables
variable "dataone_s3_bucket" {
  description = "S3 bucket for DataOne file uploads"
  type        = string
  default     = ""
}

variable "dataone_s3_key_prefix" {
  description = "S3 key prefix for DataOne file uploads"
  type        = string
  default     = "dataone/exports"
}
