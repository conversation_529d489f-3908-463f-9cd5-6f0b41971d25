# Outputs for SSM Parameter Store module

output "ssm_parameter_prefix" {
  description = "The SSM parameter prefix used for all parameters"
  value       = var.ssm_parameter_prefix
}

output "parameter_names" {
  description = "List of all SSM parameter names created"
  value = [
    aws_ssm_parameter.active_desc_veh_save_directory.name,
    aws_ssm_parameter.email_from_email.name,
    aws_ssm_parameter.email_smtp_server.name,
    aws_ssm_parameter.email_smtp_port.name,
    aws_ssm_parameter.email_smtp_user.name,
    aws_ssm_parameter.email_smtp_password.name,
    aws_ssm_parameter.email_to_emails_0.name,
    aws_ssm_parameter.email_to_emails_1.name,
    aws_ssm_parameter.db_host.name,
    aws_ssm_parameter.db_port.name,
    aws_ssm_parameter.db_database.name,
    aws_ssm_parameter.db_user.name,
    aws_ssm_parameter.db_password.name,
    aws_ssm_parameter.dataone_s3_bucket.name,
    aws_ssm_parameter.dataone_s3_key_prefix.name
  ]
}

output "application_parameters" {
  description = "Application-specific parameter names"
  value = {
    active_desc_veh_save_directory = aws_ssm_parameter.active_desc_veh_save_directory.name
  }
}

output "email_parameters" {
  description = "Email configuration parameter names"
  value = {
    from_email    = aws_ssm_parameter.email_from_email.name
    smtp_server   = aws_ssm_parameter.email_smtp_server.name
    smtp_port     = aws_ssm_parameter.email_smtp_port.name
    smtp_user     = aws_ssm_parameter.email_smtp_user.name
    smtp_password = aws_ssm_parameter.email_smtp_password.name
    to_emails_0   = aws_ssm_parameter.email_to_emails_0.name
    to_emails_1   = aws_ssm_parameter.email_to_emails_1.name
  }
}

output "database_parameters" {
  description = "Database configuration parameter names"
  value = {
    host     = aws_ssm_parameter.db_host.name
    port     = aws_ssm_parameter.db_port.name
    database = aws_ssm_parameter.db_database.name
    user     = aws_ssm_parameter.db_user.name
    password = aws_ssm_parameter.db_password.name
  }
}

output "dataone_parameters" {
  description = "DataOne configuration parameter names"
  value = {
    s3_bucket     = aws_ssm_parameter.dataone_s3_bucket.name
    s3_key_prefix = aws_ssm_parameter.dataone_s3_key_prefix.name
  }
}
