# SSM Parameter Store configuration for .NET Core DescribedVehicleExtract application

# Application Settings Parameters
resource "aws_ssm_parameter" "active_desc_veh_save_directory" {
  name  = "${var.ssm_parameter_prefix}/AppSettings/ActiveDescVehSaveDirectory"
  type  = "String"
  value = var.active_desc_veh_save_directory
  description = "Directory path for saving Active Described Vehicle files"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

# Email Settings Parameters
resource "aws_ssm_parameter" "email_from_email" {
  name  = "${var.ssm_parameter_prefix}/AppSettings/EmailSettings/FromEmail"
  type  = "String"
  value = var.email_from_email
  description = "From email address for notifications"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

resource "aws_ssm_parameter" "email_smtp_server" {
  name  = "${var.ssm_parameter_prefix}/AppSettings/EmailSettings/SmtpServer"
  type  = "String"
  value = var.email_smtp_server
  description = "SMTP server for email notifications"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

resource "aws_ssm_parameter" "email_smtp_port" {
  name  = "${var.ssm_parameter_prefix}/AppSettings/EmailSettings/SmtpPort"
  type  = "String"
  value = var.email_smtp_port
  description = "SMTP port for email notifications"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

resource "aws_ssm_parameter" "email_smtp_user" {
  name  = "${var.ssm_parameter_prefix}/AppSettings/EmailSettings/SmtpUser"
  type  = "SecureString"
  value = var.email_smtp_user
  description = "SMTP username for email notifications"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

resource "aws_ssm_parameter" "email_smtp_password" {
  name  = "${var.ssm_parameter_prefix}/AppSettings/EmailSettings/SmtpPassword"
  type  = "SecureString"
  value = var.email_smtp_password
  description = "SMTP password for email notifications"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

resource "aws_ssm_parameter" "email_to_emails_0" {
  name  = "${var.ssm_parameter_prefix}/AppSettings/EmailSettings/ToEmails/0"
  type  = "String"
  value = var.email_to_emails[0]
  description = "Primary recipient email address"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

resource "aws_ssm_parameter" "email_to_emails_1" {
  name  = "${var.ssm_parameter_prefix}/AppSettings/EmailSettings/ToEmails/1"
  type  = "String"
  value = length(var.email_to_emails) > 1 ? var.email_to_emails[1] : var.email_to_emails[0]
  description = "Secondary recipient email address"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

# Database Connection Parameters
resource "aws_ssm_parameter" "db_host" {
  name  = "${var.ssm_parameter_prefix}/DbConnectionInfo/Host"
  type  = "String"
  value = var.db_host
  description = "Database host for DescribedVehicleExtract application"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

resource "aws_ssm_parameter" "db_port" {
  name  = "${var.ssm_parameter_prefix}/DbConnectionInfo/Port"
  type  = "String"
  value = var.db_port
  description = "Database port for DescribedVehicleExtract application"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

resource "aws_ssm_parameter" "db_database" {
  name  = "${var.ssm_parameter_prefix}/DbConnectionInfo/Database"
  type  = "String"
  value = var.db_database
  description = "Database name for DescribedVehicleExtract application"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

resource "aws_ssm_parameter" "db_user" {
  name  = "${var.ssm_parameter_prefix}/DbConnectionInfo/User"
  type  = "SecureString"
  value = var.db_user
  description = "Database username for DescribedVehicleExtract application"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

resource "aws_ssm_parameter" "db_password" {
  name  = "${var.ssm_parameter_prefix}/DbConnectionInfo/Password"
  type  = "SecureString"
  value = var.db_password
  description = "Database password for DescribedVehicleExtract application"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

# DataOne specific settings (for DataOne export variant)
resource "aws_ssm_parameter" "dataone_s3_bucket" {
  name  = "${var.ssm_parameter_prefix}/AppSettings/DataOneSettings/S3Bucket"
  type  = "String"
  value = var.dataone_s3_bucket
  description = "S3 bucket for DataOne file uploads"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}

resource "aws_ssm_parameter" "dataone_s3_key_prefix" {
  name  = "${var.ssm_parameter_prefix}/AppSettings/DataOneSettings/S3KeyPrefix"
  type  = "String"
  value = var.dataone_s3_key_prefix
  description = "S3 key prefix for DataOne file uploads"

  tags = {
    Application = var.application
    Service     = var.service
    Environment = var.environment
    Component   = var.component
  }
}
