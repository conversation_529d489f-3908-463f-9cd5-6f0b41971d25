[{"name": "${task_friendly_name}", "image": "${image_url_name_tag}", "cpu": 2048, "memory": 4096, "essential": true, "environment": [{"name": "DOTNET_ENVIRONMENT", "value": "${environment}"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "${environment}"}, {"name": "TASKNAME", "value": "${task_friendly_name}"}, {"name": "COUNTRY", "value": "${country_iso_code}"}, {"name": "ENVIRONMENT", "value": "${environment}"}], "secrets": [{"name": "AppSettings__ActiveDescVehSaveDirectory", "valueFrom": "${ssm_parameter_prefix}/AppSettings/ActiveDescVehSaveDirectory"}, {"name": "AppSettings__EmailSettings__FromEmail", "valueFrom": "${ssm_parameter_prefix}/AppSettings/EmailSettings/FromEmail"}, {"name": "AppSettings__EmailSettings__SmtpServer", "valueFrom": "${ssm_parameter_prefix}/AppSettings/EmailSettings/SmtpServer"}, {"name": "AppSettings__EmailSettings__SmtpPort", "valueFrom": "${ssm_parameter_prefix}/AppSettings/EmailSettings/SmtpPort"}, {"name": "AppSettings__EmailSettings__SmtpUser", "valueFrom": "${ssm_parameter_prefix}/AppSettings/EmailSettings/SmtpUser"}, {"name": "AppSettings__EmailSettings__SmtpPassword", "valueFrom": "${ssm_parameter_prefix}/AppSettings/EmailSettings/SmtpPassword"}, {"name": "AppSettings__EmailSettings__ToEmails__0", "valueFrom": "${ssm_parameter_prefix}/AppSettings/EmailSettings/ToEmails/0"}, {"name": "AppSettings__EmailSettings__ToEmails__1", "valueFrom": "${ssm_parameter_prefix}/AppSettings/EmailSettings/ToEmails/1"}, {"name": "DbConnectionInfo__Host", "valueFrom": "${ssm_parameter_prefix}/DbConnectionInfo/Host"}, {"name": "DbConnectionInfo__Port", "valueFrom": "${ssm_parameter_prefix}/DbConnectionInfo/Port"}, {"name": "DbConnectionInfo__Database", "valueFrom": "${ssm_parameter_prefix}/DbConnectionInfo/Database"}, {"name": "DbConnectionInfo__User", "valueFrom": "${ssm_parameter_prefix}/DbConnectionInfo/User"}, {"name": "DbConnectionInfo__Password", "valueFrom": "${ssm_parameter_prefix}/DbConnectionInfo/Password"}], "mountPoints": [{"sourceVolume": "aisdata", "containerPath": "/app/data", "readOnly": false}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "${task_friendly_name}_${environment}", "awslogs-region": "${region}", "awslogs-stream-prefix": "dotnet-tasks"}}, "healthCheck": {"command": ["CMD-SHELL", "dotnet --info || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}, "user": "1000:1000", "workingDirectory": "/app", "stopTimeout": 120}]