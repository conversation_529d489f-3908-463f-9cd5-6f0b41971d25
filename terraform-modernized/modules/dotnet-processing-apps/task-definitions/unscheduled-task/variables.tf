# Variables for unscheduled task configuration

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "task_friendly_name" {
  description = "Friendly name of the task definition"
  type        = string
}

variable "task_arn" {
  description = "ARN of the task that will be run"
  type        = string
}

variable "cluster_arn" {
  description = "ARN of the ECS cluster where the task should run"
  type        = string
}

variable "role_arn" {
  description = "IAM role ARN for the event to use"
  type        = string
  default     = ""
}

variable "launch_type" {
  description = "ECS launch type (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
}

variable "platform_version" {
  description = "Platform version for Fargate tasks"
  type        = string
  default     = null
}

variable "network_configuration" {
  description = "Network configuration for the task"
  type = object({
    assign_public_ip = string
    security_groups  = list(string)
    subnets         = list(string)
  })
  default = null
}
