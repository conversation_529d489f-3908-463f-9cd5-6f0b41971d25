# Unscheduled task configuration for manual execution of .NET Core processing tasks

# This module provides the ability to manually run tasks without a schedule
# Useful for testing, debugging, or one-off executions

locals {
  task_name = "${var.task_friendly_name}_${var.environment}_manual"
}

# Create a CloudWatch Events rule that is disabled by default
# This can be enabled manually when needed for task execution
resource "aws_cloudwatch_event_rule" "manual_execution" {
  name        = local.task_name
  description = "Manual execution rule for ${var.task_friendly_name} .NET Core task"
  
  # Disabled by default - can be enabled manually
  state = "DISABLED"

  tags = {
    Name        = local.task_name
    Environment = var.environment
    Application = "DescribedVehicleExtract"
    Runtime     = "DotNetCore"
    Purpose     = "ManualExecution"
  }
}

# Target configuration for manual task execution
resource "aws_cloudwatch_event_target" "manual_execution" {
  rule     = aws_cloudwatch_event_rule.manual_execution.name
  arn      = var.cluster_arn
  role_arn = var.role_arn

  ecs_target {
    task_count          = 1
    task_definition_arn = var.task_arn
    launch_type         = var.launch_type
    platform_version    = var.platform_version

    # Network configuration for EC2 launch type
    dynamic "network_configuration" {
      for_each = var.launch_type == "EC2" && var.network_configuration != null ? [var.network_configuration] : []
      content {
        assign_public_ip = network_configuration.value.assign_public_ip
        security_groups  = network_configuration.value.security_groups
        subnets         = network_configuration.value.subnets
      }
    }

    # Placement constraints for EC2 instances
    dynamic "placement_constraint" {
      for_each = var.launch_type == "EC2" ? [1] : []
      content {
        type       = "memberOf"
        expression = "attribute:ecs.instance-type =~ m5.*"
      }
    }
  }
}
