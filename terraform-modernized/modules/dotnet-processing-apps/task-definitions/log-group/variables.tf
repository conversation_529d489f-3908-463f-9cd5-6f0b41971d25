# Variables for CloudWatch Log Group

variable "application" {
  description = "Application name"
  type        = string
}

variable "service" {
  description = "Service name"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "launched_by" {
  description = "Who launched this deployment"
  type        = string
}

variable "launched_on" {
  description = "When this deployment was launched"
  type        = string
}

variable "slack_contact" {
  description = "Slack contact for notifications"
  type        = string
}

variable "build_number" {
  description = "Build number for deployment tracking"
  type        = string
}

variable "component" {
  description = "Component name"
  type        = string
}

variable "name" {
  description = "Name identifier for the log group"
  type        = string
}

variable "log_group_name" {
  description = "Name of the CloudWatch log group"
  type        = string
}

variable "retention_in_days" {
  description = "Number of days to retain log events"
  type        = number
  default     = 7
}
