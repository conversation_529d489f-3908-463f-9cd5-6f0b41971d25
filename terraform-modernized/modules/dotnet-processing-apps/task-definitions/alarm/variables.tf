# Variables for CloudWatch Alarms

variable "application" {
  description = "Application name"
  type        = string
}

variable "service" {
  description = "Service name"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "launched_by" {
  description = "Who launched this deployment"
  type        = string
}

variable "launched_on" {
  description = "When this deployment was launched"
  type        = string
}

variable "slack_contact" {
  description = "Slack contact for notifications"
  type        = string
}

variable "build_number" {
  description = "Build number for deployment tracking"
  type        = string
}

variable "component" {
  description = "Component name"
  type        = string
}

variable "name" {
  description = "Name identifier for the alarm"
  type        = string
}

variable "log_group_name" {
  description = "Name of the CloudWatch log group to monitor"
  type        = string
}

variable "alarm_action_arn" {
  description = "ARN of the SNS topic for alarm notifications"
  type        = string
}

variable "alarm_description" {
  description = "Description of the alarm that monitors the metric filter results"
  type        = string
  default     = ""
}

variable "metric_filter_pattern" {
  description = "Pattern for metric filter to identify logs that should trigger an alarm"
  type        = string
  default     = "ERROR"
}

variable "metric_namespace" {
  description = "Namespace where metrics from log filters are stored"
  type        = string
  default     = "DotNetProcessingApps"
}
