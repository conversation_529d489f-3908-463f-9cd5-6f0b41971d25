# CloudWatch Alarms for .NET Core processing applications

resource "aws_cloudwatch_metric_alarm" "default_alarm" {
  alarm_name          = "TASK_${var.name}_${var.environment}_ERROR"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "${var.name}_${var.environment}_Errors"
  namespace           = var.metric_namespace
  period              = "300" # 5 minutes in seconds
  statistic           = "Sum"
  threshold           = "1"
  
  alarm_description = var.alarm_description != "" ? var.alarm_description : format(
    "Warning: An Error has been detected in .NET Core task %s_%s",
    var.name,
    var.environment,
  )
  
  alarm_actions = [var.alarm_action_arn]
  ok_actions    = [var.alarm_action_arn]

  tags = {
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
    Runtime     = "DotNetCore"
  }
}

resource "aws_cloudwatch_log_metric_filter" "default_filter" {
  name    = "${var.name}_MetricFilter"
  pattern = var.metric_filter_pattern

  log_group_name = var.log_group_name

  metric_transformation {
    name      = "${var.name}_${var.environment}_Errors"
    namespace = var.metric_namespace
    value     = "1"
  }
}

# Additional alarm for .NET Core specific errors
resource "aws_cloudwatch_metric_alarm" "dotnet_exception_alarm" {
  alarm_name          = "TASK_${var.name}_${var.environment}_DOTNET_EXCEPTION"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "${var.name}_${var.environment}_DotNetExceptions"
  namespace           = var.metric_namespace
  period              = "300"
  statistic           = "Sum"
  threshold           = "1"
  
  alarm_description = format(
    "Warning: A .NET Exception has been detected in task %s_%s",
    var.name,
    var.environment,
  )
  
  alarm_actions = [var.alarm_action_arn]
  ok_actions    = [var.alarm_action_arn]

  tags = {
    Application = var.application
    Environment = var.environment
    Service     = var.service
    Component   = var.component
    Runtime     = "DotNetCore"
  }
}

resource "aws_cloudwatch_log_metric_filter" "dotnet_exception_filter" {
  name    = "${var.name}_DotNetExceptionFilter"
  pattern = "[timestamp, level=\"ERROR\", logger, message=\"*Exception*\", ...]"

  log_group_name = var.log_group_name

  metric_transformation {
    name      = "${var.name}_${var.environment}_DotNetExceptions"
    namespace = var.metric_namespace
    value     = "1"
  }
}
