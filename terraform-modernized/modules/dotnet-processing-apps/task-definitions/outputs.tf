# Outputs for .NET Core processing app task definitions

output "task_arn" {
  description = "ARN of the ECS task definition"
  value       = module.task.task_arn
}

output "task_family" {
  description = "Family name of the ECS task definition"
  value       = module.task.task_family
}

output "task_revision" {
  description = "Revision of the ECS task definition"
  value       = module.task.task_revision
}

output "log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = module.log-group.log_group_name
}

output "log_group_arn" {
  description = "ARN of the CloudWatch log group"
  value       = module.log-group.log_group_arn
}

output "scheduled_event_rule_arn" {
  description = "ARN of the CloudWatch Events rule"
  value       = module.scheduled-event.event_rule_arn
}

output "scheduled_event_target_id" {
  description = "ID of the CloudWatch Events target"
  value       = module.scheduled-event.event_target_id
}

output "alarm_names" {
  description = "Names of the CloudWatch alarms created"
  value       = module.alarm.alarm_names
}

output "unscheduled_task_arn" {
  description = "ARN for manual task execution"
  value       = module.unscheduled-task.task_arn
}
