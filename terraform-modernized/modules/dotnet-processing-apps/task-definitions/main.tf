# Main module for .NET Core processing app task definitions

# Create the ECS task definition
module "task" {
  source = "./task"

  environment               = var.environment
  region                    = var.region
  country_iso_code          = var.country_iso_code
  task_friendly_name        = var.task_friendly_name
  container_definition_path = var.container_definition_path
  task_role_arn             = var.task_role_arn
  execution_role_arn        = var.execution_role_arn
  requires_compatibilites   = var.requires_compatibilites
  image_url_name_tag        = var.image_url_name_tag
  ssm_parameter_prefix      = var.ssm_parameter_prefix
  network_mode              = var.network_mode
  cpu                       = var.cpu
  memory                    = var.memory
}

# Create CloudWatch log group
module "log-group" {
  source = "./log-group"

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component

  name              = var.task_friendly_name
  log_group_name    = "${var.task_friendly_name}_${var.environment}"
  retention_in_days = var.retention_in_days
}

# Create scheduled event (if enabled)
module "scheduled-event" {
  source = "./scheduled-event"

  environment         = var.environment
  task_friendly_name  = var.task_friendly_name
  schedule_expression = var.schedule_expression
  role_arn            = var.event_rule_arn
  task_arn            = module.task.task_arn
  cluster_arn         = var.cluster_arn
  task_count          = var.task_count
  enabled             = var.enabled
  
  # .NET Core specific overrides
  launch_type                = "EC2"
  platform_version          = null
  assign_public_ip           = "DISABLED"
  security_groups            = var.security_groups
  subnets                    = var.subnets
}

# Create CloudWatch alarms
module "alarm" {
  source = "./alarm"

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component

  name                  = var.task_friendly_name
  log_group_name        = module.log-group.log_group_name
  alarm_action_arn      = var.alarm_action_arn
  alarm_description     = var.alarm_description
  metric_filter_pattern = var.alarm_metric_filter_pattern
}

# Create unscheduled task (for manual execution)
module "unscheduled-task" {
  source = "./unscheduled-task"

  environment        = var.environment
  task_friendly_name = var.task_friendly_name
  task_arn           = module.task.task_arn
  cluster_arn        = var.cluster_arn
  
  # .NET Core specific configuration
  launch_type      = "EC2"
  platform_version = null
  network_configuration = {
    assign_public_ip = "DISABLED"
    security_groups  = var.security_groups
    subnets         = var.subnets
  }
}
