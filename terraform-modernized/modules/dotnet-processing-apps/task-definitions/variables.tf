# Variables for .NET Core processing app task definitions

# Basic Configuration
variable "application" {
  description = "Application name"
  type        = string
}

variable "service" {
  description = "Service name"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "environment" {
  description = "Environment name (e.g., production, staging, uat)"
  type        = string
}

variable "build_number" {
  description = "Build number for deployment tracking"
  type        = string
}

variable "launched_by" {
  description = "Who launched this deployment"
  type        = string
}

variable "launched_on" {
  description = "When this deployment was launched"
  type        = string
}

variable "slack_contact" {
  description = "Slack contact for notifications"
  type        = string
}

variable "component" {
  description = "Component name"
  type        = string
}

# Task Definition Configuration
variable "country_iso_code" {
  description = "Country ISO code (e.g., US, CA)"
  type        = string
}

variable "task_friendly_name" {
  description = "Friendly name for the ECS task"
  type        = string
}

variable "container_definition_path" {
  description = "Path to the container definition JSON file"
  type        = string
}

variable "task_role_arn" {
  description = "ARN of the IAM role for the ECS task"
  type        = string
}

variable "execution_role_arn" {
  description = "ARN of the IAM role for ECS task execution"
  type        = string
}

variable "requires_compatibilites" {
  description = "Launch type compatibility requirements"
  type        = list(string)
  default     = ["EC2"]
}

variable "image_url_name_tag" {
  description = "Docker image URL with tag"
  type        = string
}

variable "ssm_parameter_prefix" {
  description = "Prefix for SSM parameters"
  type        = string
  default     = "/ais/describevehicleextract"
}

variable "network_mode" {
  description = "Network mode for the task definition"
  type        = string
  default     = "bridge"
}

variable "cpu" {
  description = "CPU units for the task definition"
  type        = number
  default     = 2048
}

variable "memory" {
  description = "Memory (MB) for the task definition"
  type        = number
  default     = 4096
}

# Scheduling Configuration
variable "schedule_expression" {
  description = "CloudWatch Events schedule expression"
  type        = string
  default     = ""
}

variable "event_rule_arn" {
  description = "ARN of the IAM role for CloudWatch Events"
  type        = string
}

variable "cluster_arn" {
  description = "ARN of the ECS cluster"
  type        = string
}

variable "task_count" {
  description = "Number of tasks to run"
  type        = number
  default     = 1
}

variable "enabled" {
  description = "Whether the scheduled task is enabled"
  type        = bool
  default     = true
}

# Network Configuration
variable "security_groups" {
  description = "List of security group IDs"
  type        = list(string)
  default     = []
}

variable "subnets" {
  description = "List of subnet IDs"
  type        = list(string)
  default     = []
}

# Logging Configuration
variable "retention_in_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 7
}

# Alarm Configuration
variable "alarm_action_arn" {
  description = "ARN for alarm actions"
  type        = string
  default     = ""
}

variable "alarm_description" {
  description = "Description for CloudWatch alarms"
  type        = string
  default     = "Alarm for .NET Core processing task"
}

variable "alarm_metric_filter_pattern" {
  description = "CloudWatch log metric filter pattern"
  type        = string
  default     = "ERROR"
}
