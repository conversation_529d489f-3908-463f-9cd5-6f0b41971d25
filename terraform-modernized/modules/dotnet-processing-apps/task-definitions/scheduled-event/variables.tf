# Variables for CloudWatch Events scheduled tasks

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "task_friendly_name" {
  description = "Friendly name of the task definition"
  type        = string
}

variable "schedule_expression" {
  description = "Cron expression for the scheduled task"
  type        = string
}

variable "role_arn" {
  description = "IAM role ARN for the event to use"
  type        = string
}

variable "task_arn" {
  description = "ARN of the task that will be run"
  type        = string
}

variable "cluster_arn" {
  description = "ARN of the ECS cluster where the task should run"
  type        = string
}

variable "task_count" {
  description = "Number of tasks that should run"
  type        = number
  default     = 1
}

variable "enabled" {
  description = "Whether the scheduled CloudWatch event should be enabled"
  type        = bool
  default     = false
}

variable "launch_type" {
  description = "ECS launch type (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
}

variable "platform_version" {
  description = "Platform version for Fargate tasks"
  type        = string
  default     = null
}

variable "assign_public_ip" {
  description = "Whether to assign a public IP address"
  type        = string
  default     = "DISABLED"
}

variable "security_groups" {
  description = "List of security group IDs"
  type        = list(string)
  default     = []
}

variable "subnets" {
  description = "List of subnet IDs"
  type        = list(string)
  default     = []
}
