# CloudWatch Events for scheduled .NET Core processing tasks

resource "aws_cloudwatch_event_rule" "default" {
  name                = "${var.task_friendly_name}_${var.environment}_schedule"
  description         = "Scheduled event for the ${var.task_friendly_name}_${var.environment} .NET Core processing app"
  schedule_expression = "cron(${var.schedule_expression})"
  role_arn            = var.role_arn
  state               = var.enabled ? "ENABLED" : "DISABLED"

  tags = {
    Name        = "${var.task_friendly_name}_${var.environment}_schedule"
    Environment = var.environment
    Application = "DescribedVehicleExtract"
    Runtime     = "DotNetCore"
  }

  lifecycle {
    ignore_changes = [state]
  }
}

resource "aws_cloudwatch_event_target" "default" {
  rule     = aws_cloudwatch_event_rule.default.name
  arn      = var.cluster_arn
  role_arn = var.role_arn

  ecs_target {
    task_count          = var.task_count
    task_definition_arn = var.task_arn
    launch_type         = var.launch_type
    platform_version    = var.platform_version

    # Network configuration for EC2 launch type
    dynamic "network_configuration" {
      for_each = var.launch_type == "EC2" && length(var.subnets) > 0 ? [1] : []
      content {
        assign_public_ip = var.assign_public_ip
        security_groups  = var.security_groups
        subnets         = var.subnets
      }
    }

    # Placement constraints for EC2 instances
    dynamic "placement_constraint" {
      for_each = var.launch_type == "EC2" ? [1] : []
      content {
        type       = "memberOf"
        expression = "attribute:ecs.instance-type =~ m5.*"
      }
    }
  }
}
