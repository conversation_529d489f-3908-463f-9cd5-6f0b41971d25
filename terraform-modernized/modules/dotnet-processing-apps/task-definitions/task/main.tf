# ECS Task Definition for .NET Core processing applications

# Template the container definition JSON file
data "template_file" "container_definition" {
  template = file(var.container_definition_path)

  vars = {
    task_friendly_name    = var.task_friendly_name
    image_url_name_tag    = var.image_url_name_tag
    environment          = var.environment
    country_iso_code     = var.country_iso_code
    region               = var.region
    ssm_parameter_prefix = var.ssm_parameter_prefix
  }
}

# Create the ECS task definition
resource "aws_ecs_task_definition" "task_definition" {
  family                   = var.task_friendly_name
  container_definitions    = data.template_file.container_definition.rendered
  requires_compatibilities = var.requires_compatibilites
  network_mode            = var.network_mode
  cpu                     = var.cpu
  memory                  = var.memory
  task_role_arn           = var.task_role_arn
  execution_role_arn      = var.execution_role_arn

  # Volume configuration for shared data
  volume {
    name = "aisdata"

    efs_volume_configuration {
      file_system_id          = var.efs_file_system_id
      root_directory          = "/"
      transit_encryption      = "ENABLED"
      transit_encryption_port = 2049
      
      authorization_config {
        access_point_id = var.efs_access_point_id
        iam             = "ENABLED"
      }
    }
  }

  # Placement constraints for EC2 launch type
  placement_constraints {
    type = "memberOf"
    expression = "attribute:ecs.instance-type =~ m5.*"
  }

  tags = {
    Name        = var.task_friendly_name
    Environment = var.environment
    Application = "DescribedVehicleExtract"
    Runtime     = "DotNetCore"
    Version     = "8.0"
  }

  lifecycle {
    create_before_destroy = true
  }
}
