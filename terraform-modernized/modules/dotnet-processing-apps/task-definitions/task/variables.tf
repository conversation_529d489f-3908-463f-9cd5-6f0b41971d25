# Variables for ECS task definition

variable "task_friendly_name" {
  description = "Friendly name for the ECS task"
  type        = string
}

variable "container_definition_path" {
  description = "Path to the container definition JSON file"
  type        = string
}

variable "image_url_name_tag" {
  description = "Docker image URL with tag"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "country_iso_code" {
  description = "Country ISO code"
  type        = string
}

variable "ssm_parameter_prefix" {
  description = "Prefix for SSM parameters"
  type        = string
}

variable "task_role_arn" {
  description = "ARN of the IAM role for the ECS task"
  type        = string
}

variable "execution_role_arn" {
  description = "ARN of the IAM role for ECS task execution"
  type        = string
}

variable "requires_compatibilites" {
  description = "Launch type compatibility requirements"
  type        = list(string)
  default     = ["EC2"]
}

variable "network_mode" {
  description = "Network mode for the task definition"
  type        = string
  default     = "bridge"
}

variable "cpu" {
  description = "CPU units for the task definition"
  type        = number
  default     = 2048
}

variable "memory" {
  description = "Memory (MB) for the task definition"
  type        = number
  default     = 4096
}

variable "efs_file_system_id" {
  description = "EFS file system ID for shared storage"
  type        = string
  default     = ""
}

variable "efs_access_point_id" {
  description = "EFS access point ID for secure access"
  type        = string
  default     = ""
}
