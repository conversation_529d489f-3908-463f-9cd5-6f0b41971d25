# Outputs for ECS task definition

output "task_arn" {
  description = "ARN of the ECS task definition"
  value       = aws_ecs_task_definition.task_definition.arn
}

output "task_family" {
  description = "Family name of the ECS task definition"
  value       = aws_ecs_task_definition.task_definition.family
}

output "task_revision" {
  description = "Revision of the ECS task definition"
  value       = aws_ecs_task_definition.task_definition.revision
}

output "task_definition_arn_without_revision" {
  description = "ARN of the task definition without revision"
  value       = replace(aws_ecs_task_definition.task_definition.arn, "/:\\d+$/", "")
}
