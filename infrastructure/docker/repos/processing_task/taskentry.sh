#!/bin/sh

# Parameters
# $1 : app to launch

# Environment variables
# $INI_BUCKET : ini bucket (same bucket used for service config packages)
# $ENVIRONMENT : environment
# $COUNTRY : country iso code
# $INI_S3_FILENAME : ini S3 file name
# $TASKNAME : Friendly name of task

# $PARENT_TASK : If this task is a child task, then $PARENT_TASK will be the task ARN of the parent task that spawned this one

if [ ! -z "${PARENT_TASK}" ]; then
    echo "============================"
    echo "This is a child task of the parent task ${PARENT_TASK}"
    echo "============================"
fi

if [ -z "${1}" ] || [ -z "${INI_BUCKET}" ] || [ -z "${ENVIRONMENT}" ] || [ -z "${COUNTRY}" ] || [ -z "${INI_S3_FILENAME}" ] || [ -z "${TASKNAME}" ]; then
    echo "The following parameters are all required (in order):"
    echo "App that should be launched"
    echo ""
    echo "The following environment variables are required:"
    echo "INI_BUCKET (INI and Service Config Bucket)"
    echo "ENVIRONMENT (Environment)"
    echo "COUNTRY (Country Iso Code)"
    echo "INI_S3_FILENAME (INI S3 File Name)"
    echo "TASKNAME (Friendly Task Name)"
    echo ""
    echo "Exiting with error code 1...."
    exit 1
fi


# Download the INIs
mkdir -p /download-inis
aws s3api get-object --bucket ${INI_BUCKET} --key ${ENVIRONMENT}/ProcessingApps/${COUNTRY}/${INI_S3_FILENAME} "/download-inis/${INI_S3_FILENAME}"
unzip -o "/download-inis/${INI_S3_FILENAME}" -d /download-inis/package/
rsync -a /download-inis/package/ /data/git/AIS-1.0/
rm -rf /download-inis

# Download and update the service configs
mkdir -p /download-service-configs
aws s3api get-object --bucket ${INI_BUCKET} --key ${ENVIRONMENT}/ProcessingApps/serviceConfigPackage.zip /download-service-configs/service-configs.zip
unzip -o /download-service-configs/service-configs.zip -d /download-service-configs/service-configs
cp -f /download-service-configs/service-configs/php/php.ini /etc/php-5.5.conf/php.ini
rm -rf /download-service-configs

# Make sure rsyslog is running
service rsyslog start

# Ensure postfix is started manually since seems to be the only way
service postfix start

# Reset owner/permissions after s3 zips are extracted to avoid carrying over permissioned from extracted contents
chown -R 48:48 /data/git/AIS-1.0/
find /data/git/AIS-1.0/ -type d -exec chmod 755 {} \;
find /data/git/AIS-1.0/ -type f -exec chmod 644 {} \;
find /data/git/AIS-1.0/ShellScripts -type f -name "*.sh" -exec chmod 744 {} \;

# Add a sleep command so the logs are ordered correctly in cloudwatch
sleep 2

# Run the specific app
# Weird parsing if the command uses &&, ||, etc. Put it in a string first
echo "cmd: ${1} ${2} ${3} ${4} ${5} ${6} ${7} ${8} ${9}"
eval "${1} ${2} ${3} ${4} ${5} ${6} ${7} ${8} ${9}"

# We need to capture the task exit code explicitly, since the following commands will overwrite it.
# This is the exit code we truely care about.
# If the command is an empty string, this will return an exit code of 0.
TASK_EXIT_CODE=$?

# Add a sleep command so the logs are ordered correctly in cloudwatch
sleep 2

echo ""
echo "-- -- Begin Log Dumping -- --"
echo ""

# Add a sleep command so the logs are ordered correctly in cloudwatch
sleep 2

# Get all logs and cat them
FILES=$(find /data/git/AIS-1.0 -type f -name '*.log')
for file in $FILES; do 
    echo ""
    echo "-- -- -- Begin Log Dump For ${file} -- -- --"
    echo ""
    cat ${file}
    echo ""
    echo "-- -- -- End Log Dump For ${file} -- -- --"
    echo ""
    
    # Add a sleep command so the logs are ordered correctly in cloudwatch
    sleep 2

done


echo ""
echo "-- -- End Log Dumping ---- "
echo ""

# Add a sleep command so the logs are ordered correctly in cloudwatch
sleep 2

# wait for emailing to be done
while ! postqueue -p | grep -q empty; do
    echo "postqueue is not empty, sleeping 30 seconds"
    sleep 30
done

# Copy email log to EFS
# if the file exists

# NOTE: Once we update the mail log to be smarter, and only copy over logs that have data from actual
# messages being sent, we should include it as part of the exit code being returned by this script.

#Define a variable to false which states mail was not copied if mail gets copied we will change its status
MAIL_COPY_EXIT_CODE=0
if [ -f /var/log/maillog ]
then
    if [ -s /var/log/maillog ]
    then
        DATE=`date '+%Y%m%d-%H%M%S%N'`
        EMAIL_LOG_NAME=${TASKNAME}${DATE}_mail.log
        mkdir -p /aisdata/$ENVIRONMENT/$COUNTRY/netdata/mail/
        grep -q 'from=' /var/log/maillog
# assign last exicuted command result to a variable
        EMAIL_SENT_STATUS=$?
        if [ "$EMAIL_SENT_STATUS" -eq "0" ]
        then
# mail was sent since we found "from=" in maillog file by grep now copy mail log to file and get MAIL_COPY_EXIT_CODE
# and updated with command result which will be 0 for success and 1 if failure
          cp /var/log/maillog "/aisdata/$ENVIRONMENT/$COUNTRY/netdata/mail/$EMAIL_LOG_NAME"
          MAIL_COPY_EXIT_CODE=$?
        fi
    else
        echo "/var/log/maillog is empty"
    fi
else
    echo "/var/log/maillog does not exist"
fi

# outupt 1=false 0=success
case ${TASK_EXIT_CODE}+${MAIL_COPY_EXIT_CODE} in
        0+0)
                COMMAND_EXECUTION_STATUS_CODE=0 # Success
                echo 'Task executed and mail copied'
                ;;
        0+1)
                COMMAND_EXECUTION_STATUS_CODE=1 # Command executed mail had valid from field but copy process failed
                echo 'Task executed and mail had valid from field but copy process failed'
                ;;
          *)
                COMMAND_EXECUTION_STATUS_CODE=2 # Command execution failed
                echo 'Command execution failed'
                ;;
esac
exit ${COMMAND_EXECUTION_STATUS_CODE}