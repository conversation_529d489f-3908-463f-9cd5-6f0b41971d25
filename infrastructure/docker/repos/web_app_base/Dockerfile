# Background Processing Base Image

FROM amazonlinux:1
LABEL maintainer="<PERSON>"
LABEL author="<PERSON>"

# Update packages and install tools
RUN yum -y update && \
    yum -y install wget nfs-utils rsyslog dos2unix curl unzip zip git sudo screen perl-Error perl-Git mysql mysql-libs ltrace ftp mailx python-iniparse scl-utils httpd24 httpd24-tools && \
    rpm -Uvh https://kojipkgs.fedoraproject.org/packages/crudini/0.9/1.el6/noarch/crudini-0.9-1.el6.noarch.rpm

# Install PHP and Dependencies
RUN yum -y install \
    libXpm php55-cli php55-common php55-pecl-jsonc php55-process php55-xml \
    t1lib php55 php55-bcmath php55-gd php55-mbstring php55-mysqlnd php55-pdo \
    php55-soap php55-xmlrpc php55-pecl-memcache php-pear && \
    mkdir -p /opt/rh/php55/root/usr/bin/ && \
    ln -s /usr/bin/php /opt/rh/php55/root/usr/bin/php

# Install PHP Pear Packages
RUN /usr/bin/pear channel-update pear.php.net && \
    /usr/bin/pear install MDB2 MDB2_Driver_mysql MDB2_Driver_mysqli XML_Parser && \
    /usr/bin/pear install --force PEAR/XML_Util-1.2.1 && \
    /usr/bin/pear install XML_Serializer-0.20.2

# Configure and install yum-update
RUN yum -y install yum-cron && \
    sed -i 's/^update_cmd.*=.*$/update_cmd = security/' /etc/yum/yum-cron.conf && \
    sed -i 's/^update_messages.*=.*$/update_messages = yes/' /etc/yum/yum-cron.conf && \
    sed -i 's/^download_updates.*=.*$/download_updates = yes/' /etc/yum/yum-cron.conf && \
    sed -i 's/^apply_updates.*=.*$/apply_updates = yes/' /etc/yum/yum-cron.conf && \
    chkconfig yum-cron on

# Install Postfix for PHP Mail
RUN yum -y install postfix && \
    chgrp 55 /usr/sbin/sendmail.postfix && \
    chmod g+s /usr/sbin/sendmail.postfix && \
    echo $'NETWORKING=yes\nHOSTNAME=localhost.localdomain' > /etc/sysconfig/network && \
    sed -i '/inet_interfaces/s/^/#/g' /etc/postfix/main.cf && \
    sed -i -r 's/^inet_protocols = .*/inet_protocols = ipv4/' /etc/postfix/main.cf && \
    sed -ie "\$a/^subject:/ WARN" /etc/postfix/header_checks  && \
    sed -i 's~^#header_checks = regexp:/etc/postfix/header_checks~header_checks = regexp:/etc/postfix/header_checks~g' /etc/postfix/main.cf  && \
    chkconfig postfix on

# Install AWS CLI
RUN curl -s "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "/tmp/awscliv2.zip" && \
    unzip /tmp/awscliv2.zip -d /tmp && \
    /tmp/aws/install

# Install specific version of MySQL and create a symbolic link
RUN yum install mysql56 -y && \
    mkdir -p /opt/rh/rh-mysql56/root/usr/bin/ && \
    ln -s /usr/bin/mysql /opt/rh/rh-mysql56/root/usr/bin/mysql && \
    ln -s /usr/bin/mysqldump /opt/rh/rh-mysql56/root/usr/bin/mysqldump

# Configure Apache
RUN mkdir -p /opt/rh/httpd24/root/etc/ && \
    mkdir -p /opt/rh/httpd24/root/var/

RUN ln -s /etc/init.d/httpd /etc/init.d/httpd24-httpd && \
    ln -s /etc/httpd /opt/rh/httpd24/root/etc/httpd && \
    ln -s /var/www /opt/rh/httpd24/root/var/www && \
    rm -f /etc/httpd/conf.d/welcome.conf && \
    rm -f /etc/httpd/conf.d/userdir.conf && \
    rm -f /etc/httpd/conf.d/php-conf.5.5 && \
    echo "<?php header('Location: http://www.aisrebates.com'); ?>" > /var/www/html/index.php && \
    echo $'User-agent: * \nDisallow: /' > /var/www/html/robots.txt


# Start services
RUN chkconfig httpd on && \
    service postfix restart

# Create volume location for connecting to mounted EFS on the host machine
VOLUME /aisdata

# Expose HTTP and HTTPS ports
EXPOSE 80 443