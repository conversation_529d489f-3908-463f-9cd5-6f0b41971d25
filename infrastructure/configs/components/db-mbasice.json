[{"TerraformVariableName": "custom_rds_snapshot_arn", "CakeArgumentName": "custom_rds_snapshot_arn", "IsOptional": true, "ValidationRegex": "^arn:aws:rds:(us\\-[a-zA-Z]+\\-\\d{1}):(\\d)+:snapshot:(rds:)?([a-zA-Z0-9-]+)$", "ValidationFailedText": "must be a valid snapshot arn (under same account/region) for seeding"}, {"TerraformVariableName": "rds_instance_class_kind", "CakeArgumentName": "rds_instance_class_kind", "IsOptional": true, "ValidationRegex": "^(testing|staging)$", "ValidationFailedText": "must be a valid instance class kind (testing or staging)"}]