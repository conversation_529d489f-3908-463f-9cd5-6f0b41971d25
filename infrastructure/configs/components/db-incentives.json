[{"TerraformVariableName": "custom_rds_snapshot_arn", "CakeArgumentName": "custom_rds_snapshot_arn", "IsOptional": true, "ValidationRegex": "^arn:aws:rds:(us\\-[a-zA-Z]+\\-\\d{1}):(\\d)+:cluster-snapshot:(rds:)?([a-zA-Z0-9-]+)(,arn:aws:rds:(us\\-[a-zA-Z]+\\-\\d{1}):(\\d)+:cluster-snapshot:(rds:)?([a-zA-Z0-9-]+)){1}$", "ValidationFailedText": "must be two valid snapshot arns (comma-separated with US ARN as first and CA ARN as second, both under same account/region) for seeding both instances"}, {"TerraformVariableName": "rds_instance_class_kind", "CakeArgumentName": "rds_instance_class_kind", "IsOptional": true, "ValidationRegex": "^(testing|staging)$", "ValidationFailedText": "must be a valid instance class kind (testing or staging)"}]