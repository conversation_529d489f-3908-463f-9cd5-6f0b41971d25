terraform {
  required_providers {
    aws = {
      version = "2.70.1"
      source = "hashicorp/aws"
    }
  }
}

provider "aws" {
  region = var.region
}

module "audit-queue" {
  source                    = "../../../modules/audit-queue-monitor"
  external_domain           = var.external_domain
  external_hosted_zone_id   = var.external_hosted_zone_id
  region                    = var.region
  s3_website_hosted_zone_id = var.s3_website_hosted_zone_id[var.region]
}

