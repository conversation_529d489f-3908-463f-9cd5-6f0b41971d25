terraform {
  required_providers {
    aws = {
      version = "3.76.0"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.internal_webstack_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

data "terraform_remote_state" "selected_webstack" {
  backend = "s3"

  config = {
    bucket = "ais.prod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/${var.onCluster ? "internal-webstack-cluster" : "internal-webstack"}"
    region = var.region
  }
}
module "route53_record-ca" {
  source                 = "../../../modules/internal-webstack-routing"
  internal_hosted_zone_id = var.internal_hosted_zone_id
  elb_route53_name       = "v1.ca.authoring"
  alb_dns_name           = data.terraform_remote_state.selected_webstack.outputs.internal-webstack-CA_alb_dns_name
  alb_zone_id            = data.terraform_remote_state.selected_webstack.outputs.internal-webstack-CA_alb_zone_id
}

module "route53_record-us" {
  source                 = "../../../modules/internal-webstack-routing"
  internal_hosted_zone_id = var.internal_hosted_zone_id
  elb_route53_name       = "v1.us.authoring"
  alb_dns_name           = data.terraform_remote_state.selected_webstack.outputs.internal-webstack-US_alb_dns_name
  alb_zone_id            = data.terraform_remote_state.selected_webstack.outputs.internal-webstack-US_alb_zone_id
}


