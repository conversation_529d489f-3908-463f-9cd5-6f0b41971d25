terraform {
  required_providers {
    aws = {
      version = "3.76.0"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region
}

module "ecs-failed-runtask-trail-notifier" {
  source = "../../../modules/lambdas/ecs-failed-runtask-trail-notifier"

  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = var.service
  region                  = var.region
  region_abbreviated      = var.regions_abbreviated[var.region]
  environment             = var.environment
  component               = var.component
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  account_name                = var.account_name
  account_type                = var.account_type
  enqueue_lambda_package_path = var.enqueue_lambda_package_path
  notify_lambda_package_path  = var.notify_lambda_package_path
}

