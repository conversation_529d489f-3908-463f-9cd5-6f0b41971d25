#==============================================================
# Component Variables 
#==============================================================

variable "use_most_recent_rds_snapshot" {
  description = "Whether or not to use the most recent RDS snapshot as seed data (expected values: true/false)"
  default     = "true"
}

variable "custom_rds_snapshot_arn" {
  description = "Custom RDS snapshot identifier as seed data (takes priority over use_most_recent_rds_snapshot if provided)"
  default     = ""
}

variable "rds_instance_class_kind" {
  description = "Kind of RDS instance class that's assigned to a type of environment ('testing' or 'staging' to vary instance size)"
  default     = "testing"
}

