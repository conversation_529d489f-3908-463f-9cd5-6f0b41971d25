#==============================================================
# Component Variables 
#==============================================================

variable "use_most_recent_rds_snapshot" {
  description = "Whether or not to use the most recent RDS snapshot as seed data (expected values: true/false)"
  default     = "true"
}

variable "custom_rds_snapshot_arn" {
  # Incentives RDS component is different from the rest because it stamps out both a US and CA instance so we need two ARNs to seed. 
  # To avoid making major deployment changes at this time since incentives needs its own build to add a second variable like this one, 
  # the expected value format is "arn:us:yadda:etc,arn:ca:yadda:etc" where both ARNs are comma-separated with US first and CA second.

  description = "Custom RDS snapshot identifier as seed data (takes priority over use_most_recent_rds_snapshot)"
  default     = ""
}

variable "rds_instance_class_kind" {
  description = "Kind of RDS instance class that's assigned to a type of environment ('testing' or 'staging' to vary instance size)"
  default     = "testing"
}
