[{"name": "${task_friendly_name}-${environment}", "image": "${image_url_name_tag}", "cpu": 15360, "memory": 61440, "environment": [{"name": "INI_BUCKET", "value": "${ini_bucket}"}, {"name": "ENVIRONMENT", "value": "${environment}"}, {"name": "APPLICATION_NAME", "value": "${application_name}"}, {"name": "COUNTRY", "value": "${country_iso_code}"}, {"name": "INI_S3_FILENAME", "value": "iniPackage.zip"}, {"name": "TASKNAME", "value": "${task_friendly_name}"}, {"name": "RRRI_TOPIC_ARN", "value": "${rrri_topic_arn}"}, {"name": "DEPLOY_TRIGGER_TIMESTAMP", "value": "${deploy_timestamp}"}], "portMappings": [{"containerPort": 80, "hostPort": 80}, {"containerPort": 443, "hostPort": 443}], "mountPoints": [{"sourceVolume": "aisdata", "containerPath": "/aisdata", "readonly": false}], "command": ["sudo -u \\#48 -g \\#48 INI_BUCKET=${ini_bucket} APPLICATION_NAME=${application_name} ENVIRONMENT=${environment} COUNTRY=${country_iso_code} INI_S3_FILENAME=iniPackage.zip TASKNAME=${task_friendly_name} RRRI_TOPIC_ARN=${rrri_topic_arn} ECS_CONTAINER_METADATA_FILE=$ECS_CONTAINER_METADATA_FILE"], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "${task_friendly_name}_${environment}", "awslogs-region": "${region}", "awslogs-stream-prefix": "ecstasks"}}}]