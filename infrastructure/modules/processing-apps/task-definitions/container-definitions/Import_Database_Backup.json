[{"name": "${task_friendly_name}", "image": "${image_url_name_tag}", "cpu": 1024, "memory": 1024, "environment": [{"name": "INI_BUCKET", "value": "${ini_bucket}"}, {"name": "ENVIRONMENT", "value": "${environment}"}, {"name": "COUNTRY", "value": "${country_iso_code}"}, {"name": "INI_S3_FILENAME", "value": "iniPackage.zip"}, {"name": "TASKNAME", "value": "${task_friendly_name}"}, {"name": "DATABASE_NAME", "value": ""}, {"name": "S3_BUCKET_NAME", "value": "ais.1-0.database.backups.np.ue1"}, {"name": "BACKUP_LOCATION", "value": ""}], "mountPoints": [{"sourceVolume": "aisdata", "containerPath": "/aisdata", "readonly": false}], "command": ["sh /data/git/AIS-1.0/ShellScripts/Import_Database_Backup.sh"], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "${task_friendly_name}_${environment}", "awslogs-region": "${region}", "awslogs-stream-prefix": "ecstasks"}}}]