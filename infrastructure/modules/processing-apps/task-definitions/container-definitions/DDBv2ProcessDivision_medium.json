[{"name": "${task_friendly_name}", "image": "${image_url_name_tag}", "cpu": 512, "memory": 3072, "environment": [{"name": "INI_BUCKET", "value": "${ini_bucket}"}, {"name": "ENVIRONMENT", "value": "${environment}"}, {"name": "COUNTRY", "value": "${country_iso_code}"}, {"name": "INI_S3_FILENAME", "value": "iniPackage.zip"}, {"name": "TASKNAME", "value": "${task_friendly_name}"}, {"name": "DIVISION", "value": ""}, {"name": "DIVISIONID", "value": ""}, {"name": "COUNTRYID", "value": ""}, {"name": "PARENT_TASK", "value": ""}], "mountPoints": [{"sourceVolume": "aisdata", "containerPath": "/aisdata", "readonly": false}], "command": ["sudo -u \\#48 -g \\#48 INI_BUCKET=${ini_bucket} ENVIRONMENT=${environment} COUNTRY=${country_iso_code} INI_S3_FILENAME=iniPackage.zip TASKNAME=${task_friendly_name} ECS_CONTAINER_METADATA_FILE=$ECS_CONTAINER_METADATA_FILE DIVISION=\"$DIVISION\" DIVISIONID=$DIVISIONID COUNTRYID=$COUNTRYID PARENT_TASK=\"$PARENT_TASK\" /opt/rh/php55/root/usr/bin/php /data/git/AIS-1.0/DigIRS/site_DealerDollarsBulk/dealer_dollars_bulk_v2_0_process_division.php"], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "${task_friendly_name}_${environment}", "awslogs-region": "${region}", "awslogs-stream-prefix": "ecstasks"}}}]