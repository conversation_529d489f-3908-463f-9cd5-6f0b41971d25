locals {
  name_prefix = join("-", compact([var.application, var.environment]))
}

resource "aws_sns_topic" "default" {
  name = "${local.name_prefix}-notifications"

  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Component    = var.component
  }
}

resource "aws_sqs_queue" "default" {
  name = "${local.name_prefix}-queue"

  policy = data.aws_iam_policy_document.sqs-queue-policy.json
  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Component    = var.component
  }
}

resource "aws_sns_topic_subscription" "default" {
  topic_arn = aws_sns_topic.default.arn
  protocol  = "sqs"
  endpoint  = aws_sqs_queue.default.arn
}

data "aws_iam_policy_document" "sqs-queue-policy" {
  policy_id = "__default_policy_ID"

  statement {
    sid    = "__default_statement_ID"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    actions = [
      "SQS:SendMessage",
    ]

    resources = [
      "arn:aws:sqs:${var.region}:${var.account_id}:${local.name_prefix}-queue",
    ]

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"

      values = [
        aws_sns_topic.default.arn,
      ]
    }
  }
}

