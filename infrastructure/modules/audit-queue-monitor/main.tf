resource "aws_s3_bucket" "bucket" {
  bucket = "${var.audit_queue_subdomain}.${var.external_domain}"

  website {
    index_document = "${var.index_document}"
  }
}

resource "aws_s3_bucket_policy" "bucket-policy" {
  bucket = "${aws_s3_bucket.bucket.id}"

  policy = <<POLICY
{
	"Version":"2012-10-17",
	"Statement":[{
		"Sid":"PublicReadGetObject",
			"Effect":"Allow",
		"Principal": "*",
		"Action":["s3:GetObject"],
		"Resource":["arn:aws:s3:::${aws_s3_bucket.bucket.id}/*"
		]
		}
	]
}
  POLICY
}

resource "aws_route53_record" "record" {
  zone_id = "${var.external_hosted_zone_id}"
  name    = "${aws_s3_bucket.bucket.id}"
  type    = "A"

  alias {
    name                   = "s3-website-${var.region}.amazonaws.com."
    zone_id                = "${var.s3_website_hosted_zone_id}"
    evaluate_target_health = false
  }
}

resource "aws_s3_bucket_object" "index" {
  bucket       = "${aws_s3_bucket.bucket.id}"
  key          = "${var.index_document}"
  source       = "${path.module}/${var.index_document}"
  content_type = "${var.index_document_content_type}"
}

resource "aws_s3_bucket_object" "flag-us" {
  bucket       = "${aws_s3_bucket.bucket.id}"
  key          = "${var.flag_us}"
  source       = "${path.module}/${var.flag_us}"
  content_type = "${var.flag_us_content_type}"
}

resource "aws_s3_bucket_object" "flag-ca" {
  bucket       = "${aws_s3_bucket.bucket.id}"
  key          = "${var.flag_ca}"
  source       = "${path.module}/${var.flag_ca}"
  content_type = "${var.flag_ca_content_type}"
}
