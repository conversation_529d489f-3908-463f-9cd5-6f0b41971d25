locals{
  ip_prefix = "${local.rule_prefix}-${var.application}-${var.environment}"
}

resource "aws_wafv2_ip_set" "ipv4_allowlist" {
  name               = "${local.ip_prefix}-IPv4AllowList"
  description        = local.std_allowlist_comment
  scope              = local.scope
  ip_address_version = "IPV4"
  addresses          = local.allowlist_v4_ips
}

resource "aws_wafv2_ip_set" "ipv6_allowlist" {
  name               = "${local.ip_prefix}-IPv6AllowList"
  description        = local.std_allowlist_comment
  scope              = local.scope
  ip_address_version = "IPV6"
  addresses          = local.allowlist_v6_ips
}
