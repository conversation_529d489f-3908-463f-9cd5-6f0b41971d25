locals {
  linux_name_suffix           = lower("${var.application}-${var.environment}-${local.scope}-webacl")
  linux_metrics_name          = "${local.short_name}-${var.application}-${var.environment}-${lower(local.scope)}-metrics"
}

resource "aws_wafv2_web_acl" "webacl_linux" {
  name        = "${local.short_name}-${local.linux_name_suffix}"
  description = "WebACL for AIS consumer webstack"
  scope       = local.scope
  
  depends_on = [
    aws_wafv2_ip_set.ipv4_allowlist,
    aws_wafv2_ip_set.ipv6_allowlist
  ]
  default_action {
    allow {}
  }

  rule {
    name     = "${local.rule_prefix}IPv4AllowList"
    priority = 1

    action {
      allow {}
    }

    statement {
      ip_set_reference_statement {
        arn        = aws_wafv2_ip_set.ipv4_allowlist.arn
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "${local.rule_prefix}-ipv4-allowlist-rule-metrics"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "${local.rule_prefix}IPv6AllowList"
    priority = 2

    action {
      allow {}
    }

    statement {
      ip_set_reference_statement {
        arn        = aws_wafv2_ip_set.ipv6_allowlist.arn
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "${local.rule_prefix}-ipv6-allowlist-rule-metrics"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 3

    override_action{
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWSManagedRulesCommonRuleSet-metrics"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesAdminProtectionRuleSet"
    priority = 4

    override_action{
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAdminProtectionRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWSManagedRulesAdminProtectionRuleSet-metrics"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 5

    override_action{
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWSManagedRulesKnownBadInputsRuleSet-metrics"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesSQLiRuleSet"
    priority = 6

    override_action{
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesSQLiRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWSManagedRulesSQLiRuleSet-metrics"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesLinuxRuleSet"
    priority = 7

    override_action{
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWSManagedRulesLinuxRuleSet-metrics"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "${local.rule_prefix}RateLimit"
    priority = 8

    action {
      count {}
    }

    statement {
      rate_based_statement {
        limit = var.limit
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "${local.rule_prefix}-rate-limit-rule-metrics"
      sampled_requests_enabled   = true
    }
  }


  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = local.linux_metrics_name
    sampled_requests_enabled   = true
  }
}
