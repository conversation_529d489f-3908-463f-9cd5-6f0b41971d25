/* ##########################################################################################
  Create the Scale-Out policies on the Autoscaling Group.
########################################################################################## */

resource "aws_autoscaling_policy" "ais10-webstack-scale-out" {
  name                   = "${var.application}-${var.component}-${var.environment}-scale-out"
  autoscaling_group_name = aws_autoscaling_group.ais10-webstack-asg.name

  adjustment_type    = "ChangeInCapacity"
  scaling_adjustment = var.asp_scale_out_adjustment

  cooldown = var.asp_scale_out_cooldown
}

/* ##########################################################################################
  Create the Scale-In policies on the Autoscaling Group.
########################################################################################## */

resource "aws_autoscaling_policy" "ais10-webstack-scale-in" {
  name                   = "${var.application}-${var.component}-${var.environment}-scale-in"
  autoscaling_group_name = aws_autoscaling_group.ais10-webstack-asg.name

  adjustment_type    = "ChangeInCapacity"
  scaling_adjustment = var.asp_scale_in_adjustment

  cooldown = var.asp_scale_in_cooldown
}

