###############################################################
# Pass-through Variables
###############################################################

variable "application" {
}

variable "application_abbreviated" {
}

variable "service" {
}

variable "component" {
}

variable "component_id" {
  description = "The Component ID of the consumer-webstack"
}

variable "region" {
}

variable "region_abbreviated" {
}

variable "environment" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

variable "homenet_cidr" {
}

variable "ais_cidr" {
}

variable "remote_cidr" {
}

variable "nfs_cidr" {
}

variable "package_bucket_name" {
}

variable "country_iso_code" {
}

variable "vpc_id" {
}

variable "certificate_arn" {
}

variable "public_subnet_ids" {
  type = list(string)
}

variable "private_subnet_ids" {
  type = list(string)
}

variable "availability_zones" {
  type = list(string)
}

###############################################################
# ELB/EC2 Variables
###############################################################

variable "external_hosted_zone_id" {
  description = "AWS Id of the ais-internal hosted zone"
}

variable "elb_route53_suffix" {
  description = "Name that should be appended to the product name for the ELB Route53 record"
}

variable "alb_logs_bucket" {
  description = "S3 bucket to store the ALB logs"
}

variable "alb_logs_enabled" {
  description = "Should enable the ALB logs"
  default     = false
}

variable "ec2_key_name" {
  description = "Name of the key pair to associate with EC2 instances"
  default     = "AIS10"
}

variable "health_check_path" {
  description = "path from the root of the web server to the health check page. i.e. /digirs/healthcheck.php"
  default     = "/health-check.php"
}

variable "asg_ami" {
  description = "AMI ID for the 1.0 webstack AMI pre-configured with the LAMP stack, code, and ini deployment"
}

variable "webstack_instance_type" {
  description = "Instance side to use for 1.0 webstack"
  default     = "m5.large"
}

###############################################################
# AutoScale Variables
###############################################################

variable "asg_min_size" {
  description = "Default min number of EC2s in autoscale group"
  default     = "2"
}

variable "asg_max_size" {
  description = "Default max number of EC2s in autoscale group"
  default     = "4"
}

variable "asg_desired_capacity" {
  description = "Default desired number of EC2s in autoscale group"
  default     = "2"
}

variable "asg_min_elb_capacity" {
  description = "Default autoscale elb capacity"
  default     = "2"
}

variable "asp_scale_out_adjustment" {
  description = "Instance side to use for 1.0 webstack"
  default     = "1"
}

variable "asp_scale_out_cooldown" {
  description = "Instance side to use for 1.0 webstack"
  default     = "60"
}

variable "asp_scale_in_adjustment" {
  description = "Instance side to use for 1.0 webstack"
  default     = "-1"
}

variable "asp_scale_in_cooldown" {
  description = "Instance side to use for 1.0 webstack"
  default     = "60"
}

variable "asg_scheduling_enabled" {
  description = "Whether or not the autoscaling schedulers are enabled for normal business hours"
}

variable "asg_scheduling_normal_map" {
  description = "Autoscaling scheduler map for normal business hour scale-up/down"
  type        = map(string)
  default = {
    "_default" = ""
  }
}

variable "asg_extended_scheduling_enabled" {
  description = "Whether or not the autoscaling schedulers are enabled for extended (change-day) business hours"
}

variable "asg_scheduling_extended_map" {
  description = "Autoscaling scheduler map for extended business hour scale-up/down on first/second of the month"
  type        = map(string)
  default = {
    "_default" = ""
  }
}

###############################################################
# CloudWatch Variables
###############################################################

variable "cw_alarm_low_cpu_threshold" {
  description = "The threshold value to use on the Low CPU CloudWatch alarm."
  default     = "30"
}

variable "cw_alarm_low_cpu_period" {
  description = "The metric period value to use on the Low CPU CloudWatch alarm."
  default     = "600"
}

variable "cw_alarm_low_cpu_evaluation_periods" {
  description = "The evaluation periods value to use on the Low CPU CloudWatch alarm."
  default     = "3"
}

variable "cw_alarm_high_cpu_threshold" {
  description = "The threshold value to use on the High CPU CloudWatch alarm."
  default     = "60"
}

variable "cw_alarm_high_cpu_period" {
  description = "The metric period value to use on the High CPU CloudWatch alarm."
  default     = "60"
}

variable "cw_alarm_high_cpu_evaluation_periods" {
  description = "The evaluation periods value to use on the High CPU CloudWatch alarm."
  default     = "2"
}

###############################################################
# Cloudwatch Event Variables
###############################################################

# variable "ec2_registry_lambda_arn" {
#   description = "ARN of the ec2 registry lambda"
# }

###############################################################
# Cloudwatch Alarm Variables
###############################################################

variable "warning_alert_arn" {
  description = "ARN of the warning alert SNS topic"
}

variable "critical_alert_arn" {
  description = "ARN of the critical alert SNS topic"
}

variable "metric_namespace" {
  description = "Namespace of the Consumer Webstack Unix metrics in cloudwatch"
  default     = "Unix-Metrics-Consumer-Webstack"
}

################################################################
# WAF Variables
################################################################

variable "waf_acl_arn_linux"{
  description = "arn of WAF resource"
}