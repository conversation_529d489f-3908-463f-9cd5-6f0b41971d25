resource "aws_route53_record" "primary" {
  zone_id = var.internal_hosted_zone_id
  name    = join("-", compact([local.rds_component_name, "master"]))
  type    = "CNAME"
  ttl     = "300"
  records = [aws_rds_cluster.primary.endpoint]
}


resource "aws_route53_record" "reader_address" {
  zone_id = var.internal_hosted_zone_id
  name    = join("-", compact([local.rds_component_name, "reader"]))
  type    = "CNAME"
  ttl     = "300"
  records = [aws_rds_cluster.primary.reader_endpoint]
}