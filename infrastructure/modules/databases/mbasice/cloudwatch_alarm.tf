##############################################
# High CPU Alarm 
##############################################
resource "aws_cloudwatch_metric_alarm" "aurora_rds_high_cpu_critical" {
  count = local.rds_instance_count
  alarm_name = join(
    "-",
    compact(
      [
        var.application,
        var.environment,
        "aurora-rds",
        var.database_identifier,
        "high-cpu-critical",
         count.index
      ],
    ),
  )
  alarm_description = "Triggered when CPU Utilization >= ${var.rds_alarm_high_cpu_threshold_critical}% for ${var.rds_evaluation_period} period(s) of ${var.rds_period} seconds."

  metric_name = "CPUUtilization"
  namespace   = "AWS/RDS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.rds_alarm_high_cpu_threshold_critical

  period             = var.rds_period
  evaluation_periods = var.rds_evaluation_period

  dimensions = {
    DBInstanceIdentifier = aws_rds_cluster_instance.primary[count.index].id
  }

  alarm_actions = [var.sns_critical_topic_arn]
  ok_actions    = [var.sns_critical_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "aurora_rds_high_cpu_warning" {
  count = local.rds_instance_count
  alarm_name = join(
    "-",
    compact(
      [
        var.application,
        var.environment,
        "aurora-rds",
        var.database_identifier,
        "high-cpu-warning",
        count.index
      ],
    ),
  )
  alarm_description = "Triggered when CPU Utilization >= ${var.rds_alarm_high_cpu_threshold_warning}% for ${var.rds_evaluation_period} period(s) of ${var.rds_period} seconds."

  metric_name = "CPUUtilization"
  namespace   = "AWS/RDS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.rds_alarm_high_cpu_threshold_warning

  period             = var.rds_period
  evaluation_periods = var.rds_evaluation_period

  dimensions = {
    DBInstanceIdentifier = aws_rds_cluster_instance.primary[count.index].id
  }

  alarm_actions = [var.sns_warning_topic_arn]
  ok_actions    = [var.sns_warning_topic_arn]
}

##############################################
# High DB Connections 
##############################################
resource "aws_cloudwatch_metric_alarm" "aurora_rds_high_db_connections_critical" {
  count = local.rds_instance_count
  alarm_name = join(
    "-",
    compact(
      [
        var.application,
        var.environment,
        "aurora-rds",
        var.database_identifier,
        "connections-high-critical",
        count.index
      ],
    ),
  )
  alarm_description = "Triggered when DB Connections >= ${var.rds_alarm_high_db_connection_critical} for ${var.rds_evaluation_period} period(s) of ${var.rds_period} seconds."

  metric_name = "DatabaseConnections"
  namespace   = "AWS/RDS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.rds_alarm_high_db_connection_critical

  period             = var.rds_period
  evaluation_periods = var.rds_evaluation_period

  dimensions = {
    DBInstanceIdentifier = aws_rds_cluster_instance.primary[count.index].id
  }

  alarm_actions = [var.sns_critical_topic_arn]
  ok_actions    = [var.sns_critical_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "aurora_rds_high_db_connections_warning" {
  count = local.rds_instance_count
  alarm_name = join(
    "-",
    compact(
      [
        var.application,
        var.environment,
        "aurora-rds",
        var.database_identifier,
        "connections-high-warning",
        count.index
      ],
    ),
  )
  alarm_description = "Triggered when DB Connections >= ${var.rds_alarm_high_db_connection_warning} for ${var.rds_evaluation_period} period(s) of ${var.rds_period} seconds."

  metric_name = "DatabaseConnections"
  namespace   = "AWS/RDS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.rds_alarm_high_db_connection_warning

  period             = var.rds_period
  evaluation_periods = var.rds_evaluation_period

  dimensions = {
    DBInstanceIdentifier = aws_rds_cluster_instance.primary[count.index].id
  }

  alarm_actions = [var.sns_warning_topic_arn]
  ok_actions    = [var.sns_warning_topic_arn]
}

############################################################
# DB Replica Lag 
############################################################
resource "aws_cloudwatch_metric_alarm" "aurora_rds_alarm_replica_lag_critical" {
  count = local.rds_instance_count
  alarm_name = join(
    "-",
    compact(
      [
        var.application,
        var.environment,
        "aurora-rds",
        var.database_identifier,
        "rep-lag-high-critical",
        count.index
      ],
    ),
  )
  alarm_description = "Triggered when Replication Lag >= ${var.rds_alarm_replica_lag_critical}s for ${var.rds_evaluation_period} period(s) of ${var.rds_period} seconds."

  metric_name = "ReplicaLag"
  namespace   = "AWS/RDS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.rds_alarm_replica_lag_critical

  period             = var.rds_period
  evaluation_periods = var.rds_evaluation_period

  dimensions = {
    DBInstanceIdentifier = aws_rds_cluster_instance.primary[count.index].id
  }

  alarm_actions = [var.sns_critical_topic_arn]
  ok_actions    = [var.sns_critical_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "aurora_rds_alarm_replica_lag_warning" {
  count = local.rds_instance_count
  alarm_name = join(
    "-",
    compact(
      [
        var.application,
        var.environment,
        "aurora-rds",
        var.database_identifier,
        "rep-lag-high-warning",
        count.index
      ],
    ),
  )
  alarm_description = "Triggered when Replication Lag >= ${var.rds_alarm_replica_lag_warning}s for ${var.rds_evaluation_period} period(s) of ${var.rds_period} seconds."

  metric_name = "ReplicaLag"
  namespace   = "AWS/RDS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.rds_alarm_replica_lag_warning

  period             = var.rds_period
  evaluation_periods = var.rds_evaluation_period

  dimensions = {
    DBInstanceIdentifier = aws_rds_cluster_instance.primary[count.index].id
  }

  alarm_actions = [var.sns_warning_topic_arn]
  ok_actions    = [var.sns_warning_topic_arn]
}

############################################################
# Low Storage
############################################################
resource "aws_cloudwatch_metric_alarm" "aurora_rds_alarm_low_storage_critical" {
  count = local.rds_instance_count 
  alarm_name = join(
    "-",
    compact(
      [
        var.application,
        var.environment,
        "aurora-rds",
        var.database_identifier,
        "storage-low-critical",
        count.index
      ],
    ),
  )
  alarm_description = "Triggered when Free Storage <= ${var.rds_alarm_low_storage_critical} bytes for ${var.rds_evaluation_period} period(s) of ${var.rds_period} seconds."

  metric_name = "FreeStorageSpace"
  namespace   = "AWS/RDS"
  dimensions = {
    DBInstanceIdentifier = aws_rds_cluster_instance.primary[count.index].id
  }

  statistic           = "Average"
  comparison_operator = "LessThanOrEqualToThreshold"
  threshold           = var.rds_alarm_low_storage_critical
  period              = var.rds_period
  evaluation_periods  = var.rds_evaluation_period

  alarm_actions = [var.sns_critical_topic_arn]
  ok_actions    = [var.sns_critical_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "aurora_rds_alarm_low_storage_warning" {
  count = local.rds_instance_count
  alarm_name = join(
    "-",
    compact(
      [
        var.application,
        var.environment,
        "aurora-rds",
        var.database_identifier,
        "storage-low-warning",
        count.index
      ],
    ),
  )
  alarm_description = "Triggered when Free Storage <= ${var.rds_alarm_low_storage_warning} bytes for ${var.rds_evaluation_period} period(s) of ${var.rds_period} seconds."

  metric_name = "FreeStorageSpace"
  namespace   = "AWS/RDS"
  dimensions = {
    DBInstanceIdentifier = aws_rds_cluster_instance.primary[count.index].id
  }

  statistic           = "Average"
  comparison_operator = "LessThanOrEqualToThreshold"
  threshold           = var.rds_alarm_low_storage_warning
  period              = var.rds_period
  evaluation_periods  = var.rds_evaluation_period

  alarm_actions = [var.sns_warning_topic_arn]
  ok_actions    = [var.sns_warning_topic_arn]
}

