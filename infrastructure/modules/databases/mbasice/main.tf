locals {
  rds_component_name = join(
    "-",
    compact([var.application, var.environment, var.database_identifier]),
  )
  rds_instance_count   =  (var.rds_multi_az ? var.replica_count : 0) + 1
}

resource "aws_rds_cluster" "primary" {
  cluster_identifier     = join(
    "-",
    compact(
      [
        local.rds_component_name, 
        "aurora-cluster"
      ]
    ),
  )
  engine                          = var.aurora_rds_engine
  engine_version                  = var.aurora_rds_engine_version_80
  allow_major_version_upgrade     = true
  copy_tags_to_snapshot           = var.rds_copy_tags_to_snapshot
  backup_retention_period         = var.rds_backup_retention
  preferred_backup_window         = var.rds_backup_window
  preferred_maintenance_window    = var.rds_maintenance_window
  apply_immediately               = var.apply_immediately
  vpc_security_group_ids          = [aws_security_group.default.id]
  db_subnet_group_name            = aws_db_subnet_group.default.name
  db_cluster_parameter_group_name = aws_rds_cluster_parameter_group.aurora_default_mysql_8.name
  port                            = var.rds_port
  enabled_cloudwatch_logs_exports = var.rds_logs_to_cloudwatch
  master_username                 = var.rds_db_root_user
  master_password                 = var.rds_db_root_pw
  database_name                   = var.rds_db_name
  snapshot_identifier             = var.rds_seed_from_snapshot_id
  lifecycle {
    ignore_changes = [snapshot_identifier]
  }

  skip_final_snapshot             = var.rds_skip_final_snapshot
  final_snapshot_identifier = join(
    "-",
    compact(
      [
        local.rds_component_name,
        "aurora-cluster",
        var.build_number,
      ],
    ),
  )

  tags = {
    Application                         = var.application
    Service                             = var.service
    Environment                         = var.environment
    Component                           = var.component
    Release                             = var.build_number
    LaunchedBy                          = var.launched_by
    LaunchedOn                          = var.launched_on
    SlackContact                        = var.slack_contact
    Name                                = "rdsc-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
    Group                               = "DB"
    KeepStopped                         = "conditional"
    KeepStopped_Condition_BusinessHours = var.rds_schedule_tag_value
  }
}

resource "aws_rds_cluster_instance" "primary" {
  identifier         = join(
    "-",
    compact(
      [
        local.rds_component_name, 
        "aurora", 
        count.index
      ]
    ),
  )
  cluster_identifier = aws_rds_cluster.primary.id
  count              =  local.rds_instance_count

  publicly_accessible        = var.rds_public_access
  db_subnet_group_name       = aws_db_subnet_group.default.name
  auto_minor_version_upgrade = var.rds_minor_upgrade
  apply_immediately          = var.apply_immediately
  engine                     = var.aurora_rds_engine
  instance_class             = var.aurora_instance_class

  tags = {
    Application  = var.application
    Service      = var.service
    Component    = var.component
    Environment  = var.environment
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "rdsci-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
    Group        = "DB"
    KeepStopped  = "conditional"
    KeepStopped_Condition_BusinessHours = var.rds_schedule_tag_value
  }
}
