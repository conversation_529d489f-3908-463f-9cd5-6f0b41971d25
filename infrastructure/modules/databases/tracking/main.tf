locals {
  rds_component_name = join(
    "-",
    compact([var.application, var.environment, var.database_identifier])
  )
  rds_instance_count = (var.rds_multi_az ? var.replica_count : 0) + 1 
}

resource "aws_rds_cluster" "primary" {
  cluster_identifier     = join(
    "-",
    compact(
      [
        local.rds_component_name, 
        "aurora-cluster"
      ]
    ),
  )
  vpc_security_group_ids = [aws_security_group.default.id]
  db_subnet_group_name   = aws_db_subnet_group.default.name

  master_username                 = var.rds_db_root_user
  master_password                 = var.rds_db_root_pw
  snapshot_identifier             = var.rds_seed_from_snapshot_id
  database_name                   = var.rds_db_name
   lifecycle {
    ignore_changes = [snapshot_identifier]
  }
  port                            = var.rds_port
  db_cluster_parameter_group_name = aws_rds_cluster_parameter_group.aurora_default_mysql_8.name
  skip_final_snapshot             = var.rds_skip_final_snapshot
  backup_retention_period         = var.rds_backup_retention
  preferred_backup_window         = var.rds_backup_window
  preferred_maintenance_window    = var.rds_maintenance_window
  engine                          = var.aurora_rds_engine
  engine_version                  = var.aurora_rds_engine_version_80
  allow_major_version_upgrade     = true
  apply_immediately               = var.apply_immediately
  copy_tags_to_snapshot           = var.rds_copy_tags_to_snapshot
  enabled_cloudwatch_logs_exports = var.rds_logs_to_cloudwatch
  final_snapshot_identifier       = join(
      "-",
      compact(
        [
          local.rds_component_name,
          "aurora-cluster",
          var.build_number,
        ],
      ),
    )
  tags = {
    Application  = var.application
    Service      = var.service
    Environment  = var.environment
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    Component   = var.component
    SlackContact = var.slack_contact
    Name = join(
      "-",
      compact(
        [
          "rdsc",
          var.region_abbreviated,
          var.application_abbreviated,
          var.environment,
          var.build_number
        ]
      )
    )
    KeepStopped                         = "conditional"
    KeepStopped_Condition_BusinessHours = var.rds_schedule_tag_value
    Group = "DB"
  }
}


resource "aws_rds_cluster_instance" "cluster_instances" {
  identifier = join(
    "-",
    compact(
      [
        local.rds_component_name, 
        "aurora", 
        count.index
      ]
    ),
  )
  cluster_identifier         = aws_rds_cluster.primary.id
  count                      = local.rds_instance_count
  instance_class             = var.aurora_instance_class
  publicly_accessible        = var.rds_public_access
  db_subnet_group_name       = aws_db_subnet_group.default.name
  auto_minor_version_upgrade = var.rds_minor_upgrade
  apply_immediately          = var.apply_immediately
  engine                     = var.aurora_rds_engine
  engine_version             = var.aurora_rds_engine_version_80
  tags = {
    Application  = var.application
    Service      = var.service
    Environment  = var.environment
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    Component    = var.component
    SlackContact = var.slack_contact
    Name = join(
      "-",
      compact(
        [
          "rdsci",
          var.region_abbreviated,
          var.application_abbreviated,
          var.environment,
          var.build_number
        ]
      )
    )
    Group = "DB"
  }
}


