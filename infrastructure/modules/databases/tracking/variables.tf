###############################################################
# Pass-through Variables
###############################################################

variable "application" {
}

variable "application_abbreviated" {
}

variable "service" {
}

variable "region" {
}

variable "region_abbreviated" {
}

variable "environment" {
}

variable "component" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

variable "vpc_id" {
}

variable "inbound_cidr_blocks" {
  type = list(string)
}

variable "private_subnet_ids" {
  type = list(string)
}

###############################################################
# Route53 Variables
###############################################################

variable "internal_domain" {
  description = "Domain to use when creating internal dns records"
}

variable "internal_hosted_zone_id" {
  description = "AWS Id of the ais-internal hosted zone"
}

###############################################################
# RDS Variables
###############################################################

variable "database_identifier" {
  description = "Our custom identifer for which database this represents"
  default     = "tracking"
}

variable "rds_seed_from_snapshot_id" {
  description = "Snapshot Identifier to be used as the seed data when creating an RDS instance"
  default     = "" # Empty == Do nothing (default is empty database)
}

variable "rds_db_name" {
  description = "The DB name to create. If omitted, no database is created initially"
  default     = "aisdb0_webtrack"
}

variable "rds_db_root_user" {
  description = "Username for the master DB user"
}

variable "rds_db_root_pw" {
  description = "Password for the master DB user. Note that this may show up in logs, and it will be stored in the state file."
}

variable "rds_backup_retention" {
  description = "The days to retain backups for. Must be 1 or greater to be a source for a Read Replica."
  default     = "30"
}

variable "rds_backup_window" {
  description = "The daily time range (in UTC) during which automated backups are created if they are enabled.  Must not overlap with maintenance_window. "
  default     = "06:00-08:00"
}

variable "rds_maintenance_window" {
  description = "The window to perform maintenance in. Syntax: ddd:hh24:mi-ddd:hh24:mi."
  default     = "Sun:08:00-Sun:11:00"
}

variable "rds_public_access" {
  description = "Bool to control if instance is publicly accessible."
  default     = "false"
}

variable "rds_minor_upgrade" {
  description = "Identifier for RDS DB"
  default     = "false"
}

variable "rds_copy_tags_to_snapshot" {
  description = "Copy tags to backup snapshot"
  default     = "true"
}

variable "rds_skip_final_snapshot" {
  description = "Destroy Environments without a Final Snapshot"
  default     = "false"
}

variable "rds_logs_to_cloudwatch" {
  description = "Export DB logs to Cloudwatch"
  type        = list(string)
  default     = ["audit", "error"]
}

variable "rds_multi_az" {
  description = "AZ for the RDS instance"
}

variable "apply_immediately" {
  description = "Whether or not updates to the db should be applied immediately or during the maintenence window."
  default     = "true"
}

variable "rds_schedule_tag_value" {
  description = "Tag value of the schedule for starting/stopping the database using Keep Stopped lambda"
  default     = ""
}

###############################################################
# RDS CloudWatch Variables
###############################################################

variable "sns_warning_topic_arn" {
  description = "The ARN for the SNS Topic for Warning Notifications"
}

variable "sns_critical_topic_arn" {
  description = "The ARN for the SNS Topic for Critical Notifications"
}

variable "rds_period" {
  description = "The metric period value to use on the RDS CloudWatch alarm."
  default     = "120"
}

variable "rds_evaluation_period" {
  description = "The evaluation periods value to use on RDS CloudWatch alarm."
  default     = "3"
}

variable "rds_alarm_high_cpu_threshold_warning" {
  description = "The threshold value to use on the Warning High CPU CloudWatch alarm."
  default     = "80"
}

variable "rds_alarm_high_cpu_threshold_critical" {
  description = "The metric value to use on the Critical High CPU CloudWatch alarm."
  default     = "96"
}

variable "rds_alarm_high_db_connection_warning" {
  description = "The threshold value to use on the Warning High Number of DB Connections CloudWatch alarm."
  default     = "75"
}

variable "rds_alarm_high_db_connection_critical" {
  description = "The metric value to use on the Critical High Number of DB Connections CloudWatch alarm."
  default     = "100"
}

variable "rds_alarm_replica_lag_warning" {
  description = "The threshold value to use on the Warning Replication Lag Time CloudWatch alarm."
  default     = "300"
}

variable "rds_alarm_replica_lag_critical" {
  description = "The metric value to use on the Critical Replication Lag Time CloudWatch alarm."
  default     = "600"
}

variable "rds_alarm_low_storage_warning" {
  description = "CloudWatch alarm threshold value to use for Low Storage warning"
  default     = "107400000000" # 100GiB (in bytes) of default 1000GiB
}

variable "rds_alarm_low_storage_critical" {
  description = "CloudWatch alarm threshold value to use for Low Storage critical"
  default     = "10740000000" # 10GiB (in bytes) of default 1000GiB
}

variable "aurora_rds_engine" {
  description = "DB engine type to use"
  default     = "aurora-mysql"
}

variable "aurora_rds_engine_version_80" {
  description = "DB engine version to use"
  default     = "8.0.mysql_aurora.3.05.2"
}

variable "rds_port" {
  description = "Database port"
  default     = "3306"
}

variable "aurora_instance_class" {
  description = "Instance type of the RDS aurora instance"
  default     = "db.r5.large"
}

variable "replica_count" {
  description = "Number of Aurora instances to spin up in the cluster"
  default     = "1"
}

variable "aurora_parameter_group_family_80" {
  description = "family for parameter group for Mysql 8.0"
  default     = "aurora-mysql8.0"
}

variable "aurora_rds_db_pg_80" {
  description = "DB engine version to use"
  default     = "default.aurora-mysql8.0"
}
