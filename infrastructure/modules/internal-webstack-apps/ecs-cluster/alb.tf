# Creating application load balancer
resource "aws_lb" "application_load_balancer" {
  name            = "${var.country_iso_code}-${var.application}-${var.environment}-iw-alb"
  internal           = true
  subnets         = [element(var.private_subnet_ids, 0), element(var.private_subnet_ids, 1), element(var.private_subnet_ids, 2)]
  security_groups = [aws_security_group.alb-sg.id]
  idle_timeout = 1800
}

# creating application load balancer target group
resource "aws_lb_target_group" "alb_tg" {
  name        = "${var.country_iso_code}-${var.application}-${var.environment}-iw-alb-tg"
  port        = 80
  protocol    = "HTTP"
  vpc_id      = var.vpc_id
  target_type = "instance"

  health_check {
    healthy_threshold   = "2"
    interval            = "300"
    protocol            = "HTTP"
    matcher             = "200"
    timeout             = "120"
    path                = "/health-check.php"
    unhealthy_threshold = "5"
  }
  depends_on = [aws_lb.application_load_balancer]
}

resource "aws_lb_listener" "alb-listener-http" {
  load_balancer_arn = aws_lb.application_load_balancer.id
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.alb_tg.arn
  }

}