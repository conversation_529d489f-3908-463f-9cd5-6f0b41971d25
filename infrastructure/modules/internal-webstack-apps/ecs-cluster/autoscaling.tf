/* ##########################################################################################
  Create the Scale-Out policies on the Autoscaling Group.
########################################################################################## */

resource "aws_autoscaling_policy" "default-scale-out" {
  name                   = "${var.country_iso_code}-${var.application}-${var.environment}-${var.component}-scale-out"
  autoscaling_group_name = aws_autoscaling_group.default.name

  adjustment_type    = "ChangeInCapacity"
  scaling_adjustment = var.asp_scale_out_adjustment

  cooldown = var.asp_scale_out_cooldown
}

/* ##########################################################################################
  Create the Scale-In policies on the Autoscaling Group.
########################################################################################## */

resource "aws_autoscaling_policy" "default-scale-in" {
  name                   = "${var.country_iso_code}-${var.application}-${var.environment}-${var.component}-scale-in"
  autoscaling_group_name = aws_autoscaling_group.default.name

  adjustment_type    = "ChangeInCapacity"
  scaling_adjustment = var.asp_scale_in_adjustment

  cooldown = var.asp_scale_in_cooldown
}

/* ##########################################################################################
  Create the schedules on the Autoscaling Group.
########################################################################################## */

resource "aws_autoscaling_schedule" "asg-schedule-normal-am" {
  count = var.asg_scheduling_enabled == "true" ? 1 : 0

  # Schedule resource for the autoscale group needed a unique name to reset the "start time".
  # The start is not specified in the resource and the time was being carried over when the recurrence was modified/updated.
  scheduled_action_name = "normal-morning-scale-up-${md5(
    lookup(
      var.asg_scheduling_normal_map,
      format("%s.morning", var.environment),
      var.asg_scheduling_normal_map["default.morning"],
    ),
  )}"
  min_size         = var.asg_min_size
  max_size         = var.asg_max_size
  desired_capacity = var.asg_desired_capacity
  recurrence = lookup(
    var.asg_scheduling_normal_map,
    format("%s.morning", var.environment),
    var.asg_scheduling_normal_map["default.morning"],
  )
  autoscaling_group_name = aws_autoscaling_group.default.name
}

resource "aws_autoscaling_schedule" "asg-schedule-normal-pm" {
  count = var.asg_scheduling_enabled == "true" ? 1 : 0

  # Schedule resource for the autoscale group needed a unique name to reset the "start time".
  # The start is not specified in the resource and the time was being carried over when the recurrence was modified/updated.
  scheduled_action_name = "normal-nightly-scale-down-${md5(
    lookup(
      var.asg_scheduling_normal_map,
      format("%s.night", var.environment),
      var.asg_scheduling_normal_map["default.night"],
    ),
  )}"
  min_size         = 0
  max_size         = 0
  desired_capacity = 0
  recurrence = lookup(
    var.asg_scheduling_normal_map,
    format("%s.night", var.environment),
    var.asg_scheduling_normal_map["default.night"],
  )
  autoscaling_group_name = aws_autoscaling_group.default.name
}

resource "aws_autoscaling_schedule" "asg-schedule-extended-am" {
  count = var.asg_extended_scheduling_enabled == "true" ? 1 : 0

  # Schedule resource for the autoscale group needed a unique name to reset the "start time".
  # The start is not specified in the resource and the time was being carried over when the recurrence was modified/updated.
  scheduled_action_name = "extended-morning-scale-up-${md5(
    lookup(
      var.asg_scheduling_extended_map,
      format("%s.morning", var.environment),
      var.asg_scheduling_extended_map["default.morning"]
    )
  )}"
  min_size         = var.asg_min_size
  max_size         = var.asg_max_size
  desired_capacity = var.asg_desired_capacity
  recurrence = lookup(
    var.asg_scheduling_extended_map,
    format("%s.morning", var.environment),
    var.asg_scheduling_extended_map["default.morning"]
  )
  autoscaling_group_name = aws_autoscaling_group.default.name
}

resource "aws_autoscaling_schedule" "asg-schedule-extended-pm" {
  count = var.asg_extended_scheduling_enabled == "true" ? 1 : 0

  # Schedule resource for the autoscale group needed a unique name to reset the "start time".
  # The start is not specified in the resource and the time was being carried over when the recurrence was modified/updated.
  scheduled_action_name = "extended-nightly-scale-down-${md5(
    lookup(
      var.asg_scheduling_extended_map,
      format("%s.night", var.environment),
      var.asg_scheduling_extended_map["default.night"]
    )
  )}"
  min_size         = 0
  max_size         = 0
  desired_capacity = 0
  recurrence = lookup(
    var.asg_scheduling_extended_map,
    format("%s.night", var.environment),
    var.asg_scheduling_extended_map["default.night"]
  )
  autoscaling_group_name = aws_autoscaling_group.default.name
}

resource "aws_autoscaling_lifecycle_hook" "terminate-hook" {
  #
  name                   = "${var.country_iso_code}-${var.application}-${var.component}-${var.environment}-ecs-termination-protection-lifecyclehook-terminate"
  autoscaling_group_name = aws_autoscaling_group.default.name
  default_result         = "CONTINUE"
  heartbeat_timeout      = 900
  lifecycle_transition   = "autoscaling:EC2_INSTANCE_TERMINATING"
}
