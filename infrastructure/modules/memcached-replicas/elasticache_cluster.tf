resource "aws_elasticache_cluster" "elasticache_memcached_cluster" {
  port                              = "11211"
  engine                            = "memcached"
  engine_version                    = "1.6.6"
  node_type                         = join(".", list("cache", var.elasticache_node_type))
  cluster_id                        = "consumer-${var.environment}-memcached"
  subnet_group_name                 = aws_elasticache_subnet_group.elastic_cache_memcached_subnet.name
  num_cache_nodes                   = 1
  security_group_ids                = [aws_security_group.elasticache_memcached.id]

  tags = {
    Application  = var.application
    Environment  = var.environment
    Component    = var.component
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "ecclstr-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-consumer-${var.environment}-memcached-${var.build_number}"
  }

}

resource "aws_elasticache_subnet_group" "elastic_cache_memcached_subnet" {
  name       = "consumer-${var.environment}-memcached"
  subnet_ids = var.private_subnet_ids
}

resource "aws_security_group" "elasticache_memcached" {
  name        = "consumer-${var.environment}-memcached"
  description = "Security Group for ElastiCache for Memcached"
  vpc_id      = var.vpc_id


  # Allow Memcached traffic
  ingress {
    from_port   = 11211
    to_port     = 11211
    protocol    = "tcp"
    cidr_blocks = [var.ec2_inbound_cidr]
  }

  tags = {
    Application  = var.application
    Environment  = var.environment
    Component    = var.component
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "secg-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-consumer-${var.environment}-memcached-${var.build_number}"
  }
}