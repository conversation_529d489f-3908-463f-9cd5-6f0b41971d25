###############################################################
# Pass-through Variables
###############################################################

variable "application" {
}

variable "application_abbreviated" {
}

variable "service" {
}

variable "region" {
}

variable "region_abbreviated" {
}

variable "environment" {
}

variable "component" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

variable "account_id" {
}

variable "vpc_id" {
}

variable "private_subnet_ids" {
  type = list(string)
}

variable "internal_hosted_zone" {
}

###############################################################
# RDS ASG/EC2 Variables
###############################################################

variable "elasticache_node_type" {
   description = "Type of EC2 instance to use"
}

variable "ec2_inbound_cidr" {
  description = "Inbound cidr block for elasticache port"
}

###############################################################
# Cloudwatch Alarm Variables
###############################################################

variable "warning_alert_arn" {
  description = "ARN of the warning alert SNS topic"
}

variable "critical_alert_arn" {
  description = "ARN of the critical alert SNS topic"
}

variable "metric_namespace" {
  description = "Namespace of the memcache metrics in cloudwatch"
  default     = "Memcached-Monitoring"
}