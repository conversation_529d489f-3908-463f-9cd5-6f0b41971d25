/*  Setup the security group for the EC2 instances / Autoscale Group. */
resource "aws_security_group" "sg-ec2-ais10-internal-webstack" {
  name        = "${var.application}-${var.component}-${var.environment}-${var.country_iso_code}-ec2"
  description = "Manages custom EC2 traffic for Internal Web Stack - ${var.country_iso_code}."
  vpc_id      = var.vpc_id

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow HTTP from ELB
  ingress {
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = [aws_security_group.sg-elb-ais10-internal-webstack.id]
  }

  # Allow HTTP from in-network
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = [var.homenet_cidr, var.remote_cidr, var.ais_cidr]
  }

  # Allow HTTPS from ELB
  ingress {
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    security_groups = [aws_security_group.sg-elb-ais10-internal-webstack.id]
  }

  # Allow HTTPS from in-network
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [var.homenet_cidr, var.remote_cidr, var.ais_cidr]
  }

  # Allow SSH from in-network
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.homenet_cidr, var.remote_cidr, var.ais_cidr]
  }

  # Allow NFS from AWS networks
  ingress {
    from_port   = 2049
    to_port     = 2049
    protocol    = "tcp"
    cidr_blocks = [var.nfs_cidr]
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    "Application"    = var.application
    "Environment"    = var.environment
    "Service"        = var.service
    "Release"        = var.build_number
    "LaunchedBy"     = var.launched_by
    "LaunchedOn"     = var.launched_on
    "SlackContact"   = var.slack_contact
    "Name"           = "sg-ec2-${var.region_abbreviated}-${var.application_abbreviated}-${var.component}-${var.environment}-${var.country_iso_code}-${var.build_number}"
    "Group"          = "WEB"
    "CountryIsoCode" = var.country_iso_code
    "Component"      = var.component
    "coxauto:ci-id"  = var.component_id
  }
}

/* ##########################################################################################
  Create the Auto Scaling Group for the current deployment.
########################################################################################## */

resource "aws_autoscaling_group" "ais10-internal-webstack-asg" {
  name                 = "${var.application}-${var.component}-${var.environment}-${var.country_iso_code}-asg" # Mike, put the component in this name
  launch_configuration = aws_launch_configuration.ais10-internal-webstack-lc.name
  force_delete         = true

  min_size         = var.asg_min_size
  max_size         = var.asg_max_size
  desired_capacity = var.asg_desired_capacity

  load_balancers   = [aws_elb.ais10_internal-webstack-elb.name]
  min_elb_capacity = var.asg_min_elb_capacity

  health_check_type = "ELB"
  health_check_grace_period = 1000

  wait_for_capacity_timeout = 0

  vpc_zone_identifier  = var.private_subnet_ids
  termination_policies = ["ClosestToNextInstanceHour", "Default"]
  enabled_metrics      = ["GroupMinSize", "GroupDesiredCapacity", "GroupInServiceInstances", "GroupPendingInstances", "GroupTerminatingInstances", "GroupTotalInstances"]

  lifecycle {
    create_before_destroy = true
  }

  tag {
    key                 = "Name"
    value               = "ec2-${var.region_abbreviated}-${var.application_abbreviated}-${var.component}-${var.environment}-${var.country_iso_code}-${var.build_number}"
    propagate_at_launch = true
  }

  tag {
    key                 = "Application"
    value               = var.application
    propagate_at_launch = true
  }

  tag {
    key                 = "Service"
    value               = var.service
    propagate_at_launch = true
  }

  tag {
    key                 = "Environment"
    value               = var.environment
    propagate_at_launch = true
  }

  tag {
    key                 = "Release"
    value               = var.build_number
    propagate_at_launch = true
  }

  tag {
    key                 = "LaunchedBy"
    value               = var.launched_by
    propagate_at_launch = true
  }

  tag {
    key                 = "LaunchedOn"
    value               = var.launched_on
    propagate_at_launch = true
  }

  tag {
    key                 = "SlackContact"
    value               = var.slack_contact
    propagate_at_launch = true
  }

  tag {
    key                 = "Group"
    value               = "WEB"
    propagate_at_launch = true
  }

  tag {
    key                 = "ssm-patch-installation"
    value               = "true"
    propagate_at_launch = true
  }

  tag {
    key                 = "AppStackTypeForSSM"
    value               = "InternalWebstack"
    propagate_at_launch = true
  }

  tag {
    key                 = "CountryIsoCode"
    value               = var.country_iso_code
    propagate_at_launch = true
  }

  tag {
    key                 = "Component"
    value               = var.component
    propagate_at_launch = true
  }

  tag {
    key                 = "coxauto:ci-id"
    value               = var.component_id
    propagate_at_launch = true
  }
}

resource "aws_autoscaling_schedule" "asg-schedule-normal-am" {
  count = var.asg_scheduling_enabled == "true" ? 1 : 0

  # Schedule resource for the autoscale group needed a unique name to reset the "start time".
  # The start is not specified in the resource and the time was being carried over when the recurrence was modified/updated.
  scheduled_action_name = "normal-morning-scale-up-${md5(
    lookup(
      var.asg_scheduling_normal_map,
      format("%s.morning", var.environment),
      var.asg_scheduling_normal_map["default.morning"],
    ),
  )}"
  min_size         = var.asg_min_size
  max_size         = var.asg_max_size
  desired_capacity = var.asg_desired_capacity
  recurrence = lookup(
    var.asg_scheduling_normal_map,
    format("%s.morning", var.environment),
    var.asg_scheduling_normal_map["default.morning"],
  )
  autoscaling_group_name = aws_autoscaling_group.ais10-internal-webstack-asg.name
}

resource "aws_autoscaling_schedule" "asg-schedule-normal-pm" {
  count = var.asg_scheduling_enabled == "true" ? 1 : 0

  # Schedule resource for the autoscale group needed a unique name to reset the "start time".
  # The start is not specified in the resource and the time was being carried over when the recurrence was modified/updated.
  scheduled_action_name = "normal-nightly-scale-down-${md5(
    lookup(
      var.asg_scheduling_normal_map,
      format("%s.night", var.environment),
      var.asg_scheduling_normal_map["default.night"],
    ),
  )}"
  min_size         = 0
  max_size         = 0
  desired_capacity = 0
  recurrence = lookup(
    var.asg_scheduling_normal_map,
    format("%s.night", var.environment),
    var.asg_scheduling_normal_map["default.night"],
  )
  autoscaling_group_name = aws_autoscaling_group.ais10-internal-webstack-asg.name
}

resource "aws_autoscaling_schedule" "asg-schedule-extended-am" {
  count = var.asg_extended_scheduling_enabled == "true" ? 1 : 0

  # Schedule resource for the autoscale group needed a unique name to reset the "start time".
  # The start is not specified in the resource and the time was being carried over when the recurrence was modified/updated.
  scheduled_action_name = "extended-morning-scale-up-${md5(
    lookup(
      var.asg_scheduling_extended_map,
      format("%s.morning", var.environment),
      var.asg_scheduling_extended_map["default.morning"]
    )
  )}"
  min_size         = var.asg_min_size
  max_size         = var.asg_max_size
  desired_capacity = var.asg_desired_capacity
  recurrence = lookup(
    var.asg_scheduling_extended_map,
    format("%s.morning", var.environment),
    var.asg_scheduling_extended_map["default.morning"]
  )
  autoscaling_group_name = aws_autoscaling_group.ais10-internal-webstack-asg.name
}

resource "aws_autoscaling_schedule" "asg-schedule-extended-pm" {
  count = var.asg_extended_scheduling_enabled == "true" ? 1 : 0

  # Schedule resource for the autoscale group needed a unique name to reset the "start time".
  # The start is not specified in the resource and the time was being carried over when the recurrence was modified/updated.
  scheduled_action_name = "extended-nightly-scale-down-${md5(
    lookup(
      var.asg_scheduling_extended_map,
      format("%s.night", var.environment),
      var.asg_scheduling_extended_map["default.night"]
    )
  )}"
  min_size         = 0
  max_size         = 0
  desired_capacity = 0
  recurrence = lookup(
    var.asg_scheduling_extended_map,
    format("%s.night", var.environment),
    var.asg_scheduling_extended_map["default.night"]
  )
  autoscaling_group_name = aws_autoscaling_group.ais10-internal-webstack-asg.name
}

resource "aws_launch_configuration" "ais10-internal-webstack-lc" {
  name_prefix     = "${var.application}-${var.component}-${var.environment}-${var.country_iso_code}"
  image_id        = var.asg_ami
  instance_type   = var.webstack_instance_type
  security_groups = [aws_security_group.sg-elb-ais10-internal-webstack.id, var.efs_security_group]

  associate_public_ip_address = false
  enable_monitoring           = true
  key_name                    = var.ec2_key_name
  iam_instance_profile        = "ais10-ec2-role"
  user_data                   = data.template_file.userdata1.rendered

  lifecycle {
    create_before_destroy = true
  }
}

data "template_file" "userdata1" {
  template = file("${path.module}/user_data.tpl")
  vars = {
    package_bucket_name = var.package_bucket_name
    ec2_environment     = var.environment
    country_iso_code    = var.country_iso_code
    region              = var.region
  }
}

