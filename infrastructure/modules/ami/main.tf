data "template_file" "user_data" {
  template = file(
    "${path.module}/user-data/${var.user_data_script_name}/script.cloudinit",
  )
  vars = {
    region = var.region
    efs_id = var.efs_id
  }
}

resource "aws_instance" "create_instance" {
  ami                  = var.base_ami_id
  instance_type        = var.instance_type
  key_name             = var.key_pair_name
  subnet_id            = element(var.private_subnet_ids, 0) # Doesn't matter which subnet
  iam_instance_profile = var.instance_profile_role
  user_data            = data.template_file.user_data.rendered
  vpc_security_group_ids = [var.temp_ec2_security_group_ids["${var.account_type}-${var.region_abbreviated}"]]

  tags = {
    Name = "temp-ami-${var.region_abbreviated}-${var.application_abbreviated}-${var.environment}-${var.build_number}"
  }
}

resource "null_resource" "wait_for_instance_to_create" {
  depends_on = [aws_instance.create_instance]

  # Depends on AWS CLI being installed with active credentials in the %homepath%\credentials file
  # Depends on AWS CLI being installed with active credentials in the %homepath%\credentials file
  provisioner "local-exec" {
    command = "aws ec2 wait instance-status-ok --instance-ids ${aws_instance.create_instance.id} --region ${var.region}"
  }
}

resource "null_resource" "stop_instance" {
  depends_on = [null_resource.wait_for_instance_to_create]

  # Depends on AWS CLI being installed with active credentials in the %homepath%\credentials file
  # Depends on AWS CLI being installed with active credentials in the %homepath%\credentials file
  provisioner "local-exec" {
    command = "aws ec2 stop-instances --instance-ids ${aws_instance.create_instance.id} --region ${var.region}"
  }
}

resource "null_resource" "wait_for_instance_to_stop" {
  depends_on = [null_resource.stop_instance]

  # Depends on AWS CLI being installed with active credentials in the %homepath%\credentials file
  # Depends on AWS CLI being installed with active credentials in the %homepath%\credentials file
  provisioner "local-exec" {
    command = "aws ec2 wait instance-stopped --instance-ids ${aws_instance.create_instance.id} --region ${var.region}"
  }
}

resource "aws_ami_from_instance" "create_ami" {
  name               = "${var.user_data_script_name}-${var.vpc_name}-v${var.build_number}"
  source_instance_id = aws_instance.create_instance.id
  depends_on         = [null_resource.wait_for_instance_to_stop]
}

resource "null_resource" "terminate_instance" {
  depends_on = [aws_ami_from_instance.create_ami]

  # Depends on AWS CLI being installed with active credentials in the %homepath%\credentials file
  # Depends on AWS CLI being installed with active credentials in the %homepath%\credentials file
  provisioner "local-exec" {
    command = "aws ec2 terminate-instances --instance-ids ${aws_instance.create_instance.id} --region ${var.region}"
  }
}

