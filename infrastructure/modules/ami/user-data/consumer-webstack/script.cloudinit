#cloud-config
runcmd:
  #Update to latest
  - "yum update -y"
  
  #Add tools
  - "yum -y install nfs-utils scl-utils git screen perl-Error perl-Git mysql mysql-libs ltrace ftp scl-utils"

  #Add Apache/PHP/Memcache and Dependancies
  - "yum -y install httpd24 httpd24-tools libXpm php55-cli php55-common php55-pecl-jsonc php55-process php55-xml t1lib php55 php55-bcmath php55-gd php55-mbstring php55-mysqlnd php55-pdo php55-soap php55-xmlrpc php55-pecl-memcache"

  #Configure pathing for backwards compatibility of apache/php/aws
  - "mkdir -p /opt/rh/php55/root/usr/bin/ && ln -s /usr/bin/php /opt/rh/php55/root/usr/bin/php && ln -s /usr/bin/pear /opt/rh/php55/root/usr/bin/pear"
  - "mkdir -p {/opt/rh/httpd24/root/etc/,/opt/rh/httpd24/root/var/www} && ln -s /etc/init.d/httpd /etc/init.d/httpd24-httpd && ln -s /etc/httpd /opt/rh/httpd24/root/etc/httpd && ln -s /var/www /opt/rh/httpd24/root/var/www"
  - "mkdir -p /usr/local/bin/aws && ln -s /usr/bin/aws /usr/local/bin/aws"

  #Configure and install yum-update
  - "yum install -y yum-cron"
  - "sed -i 's/^update_cmd.*=.*$/update_cmd = security/' /etc/yum/yum-cron.conf"
  - "sed -i 's/^update_messages.*=.*$/update_messages = yes/' /etc/yum/yum-cron.conf"
  - "sed -i 's/^download_updates.*=.*$/download_updates = yes/' /etc/yum/yum-cron.conf"
  - "sed -i 's/^apply_updates.*=.*$/apply_updates = yes/' /etc/yum/yum-cron.conf"
  - "service yum-cron start"
  - "chkconfig yum-cron on"

  #Mount aisdata for reference files
  - "mkdir -p /aisdata/"
  - "chown ec2-user:ec2-user /aisdata/"
  - "echo \"${efs_id}.efs.${region}.amazonaws.com:/ /aisdata nfs4 nfsvers=4.1,noresvport,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2 0 0\" | tee -a /etc/fstab"
  - "mount -a -t nfs4"

  #Create Code Directory
  - "mkdir -p /data/git/AIS-1.0/"
  - "chown ec2-user:ec2-user /data/git/AIS-1.0/"
  - "setfacl -d -R -m user:ec2-user:rwx -m user:apache:rwx /data/git"
  - "setfacl -R -m user:ec2-user:rwx -m user:apache:rwx /data/git/*"

  #Install and Configure Postfix
  - "yum -y install postfix"
  - "sed -ie \"\\$a/^subject:/ WARN\" /etc/postfix/header_checks"
  - "sed -i 's~^#header_checks = regexp:/etc/postfix/header_checks~header_checks = regexp:/etc/postfix/header_checks~g' /etc/postfix/main.cf"
  - "service postfix restart"

  #Install Pear modules and then beta Pear modules (that's why it's a seperate step)
  - "/usr/bin/pear channel-update pear.php.net"
  - "/usr/bin/pear install MDB2 MDB2_Driver_mysql MDB2_Driver_mysqli XML_Parser"
  - "/usr/bin/pear install --force PEAR/XML_Util-1.2.1"
  - "/usr/bin/pear install XML_Serializer-0.20.2"

  #Remove Welcome Page and User directories from httpd
  - "rm -f /etc/httpd/conf.d/welcome.conf"
  - "rm -f /etc/httpd/conf.d/userdir.conf"
  - "rm -f /etc/httpd/conf.d/php-conf.5.5"

  #Create robots.txt and Index redirect
  - "echo \"<?php header('Location: http://www.aisrebates.com'); ?>\" > /var/www/html/index.php"
  - "echo $'User-agent: * \nDisallow: /' > /var/www/html/robots.txt"

  #Start httpd and configure on restart
  - "service httpd start"
  - "chkconfig httpd on"

  #Start SSM
  - "start amazon-ssm-agent"
  
  #Setup CloudWatchMonitor for Memory Metrics
  - "yum -y install python-pip"
  - "pip -q install cloudwatchmon"
  - "crontab -l | { cat; echo \"* * * * * mon-put-instance-stats.py --mem-util --disk-space-util --disk-path=/ --from-cron\"; } | crontab -"
