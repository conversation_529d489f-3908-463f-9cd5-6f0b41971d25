name: Deploy Terraform Component

on:
  workflow_dispatch:
    inputs:
      target:
        description: 'Target task to run'
        required: true
        default: 'Default'
        type: choice
        options:
          - Default
          - Plan
          - Apply
          - Destroy
      environment:
        description: 'Environment name to deploy'
        required: true
        type: string
      component:
        description: 'Component name to deploy'
        required: true
        type: string
      account:
        description: 'AWS account'
        required: false
        default: 'awsaaianp'
        type: string
      region:
        description: 'AWS region'
        required: false
        default: 'us-east-1'
        type: string
      on_cluster:
        description: 'On cluster flag'
        required: false
        type: string
      profile_name:
        description: 'AWS profile name'
        required: false
        type: string

env:
  APPLICATION_ABBREVIATION: "ais10"
  TERRAFORM_VERSION: "0.13.7"
  DEFAULT_REGION_NAME: "us-east-1"
  ARTIFACT_PATH: "./artifacts"
  LAMBDA_SOURCE_PATH: "./infrastructure/lambda"
  LAMBDA_ARTIFACT_PATH: "./artifacts/lambda"
  LAMBDA_ARTIFACT_FOLDER_PATH: "./artifacts/lambda"
  MYSQL_VERSION_SUFFIX: "57"

jobs:
  safety-check:
    name: Safety Check
    runs-on: ubuntu-latest
    outputs:
      base-environment: ${{ steps.determine-env.outputs.base-environment }}
      account-type: ${{ steps.determine-env.outputs.account-type }}
      region-abbreviation: ${{ steps.determine-env.outputs.region-abbreviation }}
      tf-environment-directory: ${{ steps.determine-env.outputs.tf-environment-directory }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set default inputs
        id: set-defaults
        run: |
          echo "ENVIRONMENT=${{ github.event.inputs.environment }}" >> $GITHUB_ENV
          echo "COMPONENT=${{ github.event.inputs.component }}" >> $GITHUB_ENV
          echo "REGION=${{ github.event.inputs.region || env.DEFAULT_REGION_NAME }}" >> $GITHUB_ENV
          echo "ACCOUNT=${{ github.event.inputs.account || 'awsaaianp' }}" >> $GITHUB_ENV
          echo "PROFILE_NAME=${{ github.event.inputs.profile_name || format('{0}:PowerUser', github.event.inputs.account || 'awsaaianp') }}" >> $GITHUB_ENV
          echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_ENV
          echo "LAUNCHED_BY=${{ github.actor }}" >> $GITHUB_ENV
          echo "LAUNCHED_ON=$(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_ENV

      - name: Determine base environment and paths
        id: determine-env
        run: |
          # Determine base environment
          ENVIRONMENT="${{ env.ENVIRONMENT }}"
          if [[ "$ENVIRONMENT" == scratch* ]]; then
            BASE_ENV="scratch"
          elif [[ "$ENVIRONMENT" == uat* ]]; then
            BASE_ENV="uat"
          elif [[ "$ENVIRONMENT" == qamain* ]]; then
            BASE_ENV="qamain"
          elif [[ "$ENVIRONMENT" == qarapid* ]]; then
            BASE_ENV="qarapid"
          elif [[ "$ENVIRONMENT" == homenet* ]]; then
            BASE_ENV="homenet"
          elif [[ "$ENVIRONMENT" == production* ]]; then
            BASE_ENV="production"
          elif [[ "$ENVIRONMENT" == nonprod* ]]; then
            BASE_ENV="nonprod"
          else
            BASE_ENV="$ENVIRONMENT"
          fi
          
          # Determine account type
          if [[ "$BASE_ENV" == "production" ]]; then
            ACCOUNT_TYPE="prod"
          else
            ACCOUNT_TYPE="nonprod"
          fi
          
          # Calculate region abbreviation (us-east-1 => ue1)
          REGION="${{ env.REGION }}"
          REGION_PARTS=(${REGION//-/ })
          REGION_ABBREV="${REGION_PARTS[0]:0:1}${REGION_PARTS[1]:0:1}${REGION_PARTS[2]}"
          
          # Set paths
          TF_ENV_DIR="./infrastructure/environments/${BASE_ENV}/${{ env.COMPONENT }}"
          
          echo "base-environment=$BASE_ENV" >> $GITHUB_OUTPUT
          echo "account-type=$ACCOUNT_TYPE" >> $GITHUB_OUTPUT
          echo "region-abbreviation=$REGION_ABBREV" >> $GITHUB_OUTPUT
          echo "tf-environment-directory=$TF_ENV_DIR" >> $GITHUB_OUTPUT
          
          echo "BASE_ENVIRONMENT=$BASE_ENV" >> $GITHUB_ENV
          echo "ACCOUNT_TYPE=$ACCOUNT_TYPE" >> $GITHUB_ENV
          echo "REGION_ABBREVIATION=$REGION_ABBREV" >> $GITHUB_ENV
          echo "TF_ENVIRONMENT_DIRECTORY=$TF_ENV_DIR" >> $GITHUB_ENV

      - name: Validate inputs
        run: |
          # Verify required arguments
          if [[ -z "${{ env.ENVIRONMENT }}" ]]; then
            echo "Error: Must specify an environment name to be deployed with Terraform"
            exit 1
          fi
          
          if [[ -z "${{ env.COMPONENT }}" ]]; then
            echo "Error: Must specify a component name for the environment to be deployed with Terraform"
            exit 1
          fi
          
          if [[ -z "${{ env.REGION }}" ]]; then
            echo "Error: Must specify a region name for the environment to be deployed with Terraform"
            exit 1
          fi

      - name: Verify paths exist
        run: |
          # Verify environment/component directory exists
          if [[ ! -d "${{ env.TF_ENVIRONMENT_DIRECTORY }}" ]]; then
            echo "Error: Invalid combination of environment and component: can't find path ${{ env.TF_ENVIRONMENT_DIRECTORY }}"
            exit 1
          fi
          
          # Verify shared variable files exist
          SHARED_VAR_ACCOUNT="./infrastructure/shared-variables.${{ env.ACCOUNT_TYPE }}.tf"
          SHARED_VAR_REGION="./infrastructure/shared-variables.${{ env.ACCOUNT_TYPE }}.${{ env.REGION_ABBREVIATION }}.tf"
          
          if [[ ! -f "$SHARED_VAR_ACCOUNT" ]]; then
            echo "Error: Unable to find shared variable file for account at $SHARED_VAR_ACCOUNT"
            exit 1
          fi
          
          if [[ ! -f "$SHARED_VAR_REGION" ]]; then
            echo "Error: Unable to find shared variable file for region at $SHARED_VAR_REGION"
            exit 1
          fi

      - name: Install jq for JSON parsing
        run: sudo apt-get update && sudo apt-get install -y jq

      - name: Validate component configuration
        run: |
          chmod +x .github/scripts/terraform-config-parser.sh
          
          # Find config file
          CONFIG_FILE=""
          if CONFIG_FILE=$(.github/scripts/terraform-config-parser.sh find-config "${{ env.BASE_ENVIRONMENT }}" "${{ env.COMPONENT }}"); then
            echo "Found config file: $CONFIG_FILE"
            
            # Validate JSON format
            .github/scripts/terraform-config-parser.sh validate-json "$CONFIG_FILE"
            
            # Validate lambda directories exist
            .github/scripts/terraform-config-parser.sh validate-lambdas "$CONFIG_FILE" "${{ env.LAMBDA_SOURCE_PATH }}" "${{ env.COMPONENT }}"
            
            echo "✓ Component configuration validation passed"
          else
            echo "No config file found - component has no specific configuration parameters"
          fi

  setup-nodejs:
    name: Setup Node.js
    runs-on: ubuntu-latest
    needs: safety-check
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'npm'
          cache-dependency-path: '**/package.json'

  package-lambdas:
    name: Package Lambda Functions
    runs-on: ubuntu-latest
    needs: [safety-check, setup-nodejs]
    env:
      BASE_ENVIRONMENT: ${{ needs.safety-check.outputs.base-environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'

      - name: Install jq for JSON parsing
        run: sudo apt-get update && sudo apt-get install -y jq

      - name: Create artifact directories
        run: |
          mkdir -p ${{ env.ARTIFACT_PATH }}
          rm -rf ${{ env.LAMBDA_ARTIFACT_PATH }}
          mkdir -p ${{ env.LAMBDA_ARTIFACT_PATH }}

      - name: Load component configuration and validate
        id: load-config
        env:
          BASE_ENVIRONMENT: ${{ needs.safety-check.outputs.base-environment }}
        run: |
          chmod +x .github/scripts/terraform-config-parser.sh
          
          # Find config file
          CONFIG_FILE=""
          if CONFIG_FILE=$(.github/scripts/terraform-config-parser.sh find-config "$BASE_ENVIRONMENT" "${{ github.event.inputs.component }}"); then
            echo "Found config file: $CONFIG_FILE"
            echo "config-file=$CONFIG_FILE" >> $GITHUB_OUTPUT
            
            # Validate JSON format
            .github/scripts/terraform-config-parser.sh validate-json "$CONFIG_FILE"
            
            # Get lambda folders
            LAMBDA_FOLDERS=$(.github/scripts/terraform-config-parser.sh get-lambda-folders "$CONFIG_FILE")
            echo "lambda-folders<<EOF" >> $GITHUB_OUTPUT
            echo "$LAMBDA_FOLDERS" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT
            
            # Validate lambda directories
            .github/scripts/terraform-config-parser.sh validate-lambdas "$CONFIG_FILE" "${{ env.LAMBDA_SOURCE_PATH }}" "${{ github.event.inputs.component }}"
          else
            echo "No config file found - proceeding without lambda packaging"
            echo "config-file=" >> $GITHUB_OUTPUT
            echo "lambda-folders=" >> $GITHUB_OUTPUT
          fi

      - name: Package lambda functions
        if: steps.load-config.outputs.lambda-folders != ''
        run: |
          echo "Packaging lambda functions..."
          
          # Read lambda folders from previous step
          LAMBDA_FOLDERS='${{ steps.load-config.outputs.lambda-folders }}'
          
          while IFS= read -r lambda_folder; do
            if [[ -n "$lambda_folder" ]]; then
              echo "Packaging lambda: $lambda_folder"
              
              lambda_source_path="${{ env.LAMBDA_SOURCE_PATH }}/$lambda_folder"
              lambda_zip_path="${{ env.LAMBDA_ARTIFACT_PATH }}/${lambda_folder}.zip"
              
              # Install npm dependencies if package.json exists
              if [[ -f "$lambda_source_path/package.json" ]]; then
                echo "Found package.json, running npm install for $lambda_folder"
                cd "$lambda_source_path"
                npm install --production
                cd - > /dev/null
              fi
              
              # Create zip file
              echo "Creating zip file: $lambda_zip_path"
              cd "$lambda_source_path"
              zip -r "$lambda_zip_path" . -x "*.git*" "node_modules/.cache/*" "*.DS_Store"
              cd - > /dev/null
              
              echo "✓ Successfully packaged $lambda_folder"
            fi
          done <<< "$LAMBDA_FOLDERS"

      - name: Upload lambda artifacts
        if: steps.load-config.outputs.lambda-folders != ''
        uses: actions/upload-artifact@v4
        with:
          name: lambda-packages
          path: ${{ env.LAMBDA_ARTIFACT_PATH }}
          retention-days: 1

  setup-terraform:
    name: Setup Terraform
    runs-on: ubuntu-latest
    needs: safety-check
    steps:
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Verify Terraform version
        run: terraform --version

  prepare-terraform-args:
    name: Prepare Terraform Arguments
    runs-on: ubuntu-latest
    needs: safety-check
    outputs:
      terraform-args: ${{ steps.build-args.outputs.terraform-args }}
    env:
      BASE_ENVIRONMENT: ${{ needs.safety-check.outputs.base-environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install jq for JSON parsing
        run: sudo apt-get update && sudo apt-get install -y jq

      - name: Load component configuration
        id: load-config
        run: |
          chmod +x .github/scripts/terraform-config-parser.sh
          
          # Find config file
          CONFIG_FILE=""
          if CONFIG_FILE=$(.github/scripts/terraform-config-parser.sh find-config "${{ env.BASE_ENVIRONMENT }}" "${{ github.event.inputs.component }}"); then
            echo "Found config file: $CONFIG_FILE"
            echo "config-file=$CONFIG_FILE" >> $GITHUB_OUTPUT
            
            # Validate JSON format
            .github/scripts/terraform-config-parser.sh validate-json "$CONFIG_FILE"
            
            # Validate parameter values
            .github/scripts/terraform-config-parser.sh validate-parameters "$CONFIG_FILE" "${{ github.event.inputs.component }}"
            
            # Get component-specific Terraform arguments
            COMPONENT_ARGS=$(.github/scripts/terraform-config-parser.sh get-terraform-args "$CONFIG_FILE" "${{ env.LAMBDA_ARTIFACT_PATH }}")
            echo "component-args=$COMPONENT_ARGS" >> $GITHUB_OUTPUT
            
            echo "✓ Component configuration processed successfully"
          else
            echo "No config file found - proceeding with global arguments only"
            echo "config-file=" >> $GITHUB_OUTPUT
            echo "component-args=" >> $GITHUB_OUTPUT
          fi

      - name: Build Terraform arguments
        id: build-args
        run: |
          # Build global terraform arguments
          ARGS=""
          ARGS="$ARGS -var \"region=${{ github.event.inputs.region || env.DEFAULT_REGION_NAME }}\""
          ARGS="$ARGS -var \"build_number=${{ github.run_number }}\""
          ARGS="$ARGS -var \"environment=${{ github.event.inputs.environment }}\""
          ARGS="$ARGS -var \"launched_by=${{ github.actor }}\""
          ARGS="$ARGS -var \"launched_on=$(date -u +%Y-%m-%dT%H:%M:%SZ)\""
          ARGS="$ARGS -var \"component=${{ github.event.inputs.component }}\""
          
          # Note: In the original Cake script, this gets the Slack channel from hnEnvironment
          # You'll need to adapt this based on how you want to handle configuration
          ARGS="$ARGS -var \"slack_contact=+general\""
          
          if [[ -n "${{ github.event.inputs.on_cluster }}" ]]; then
            ARGS="$ARGS -var \"onCluster=${{ github.event.inputs.on_cluster }}\""
          fi
          
          # Add component-specific arguments
          COMPONENT_ARGS="${{ steps.load-config.outputs.component-args }}"
          if [[ -n "$COMPONENT_ARGS" ]]; then
            ARGS="$ARGS $COMPONENT_ARGS"
          fi
          
          echo "Final Terraform arguments: $ARGS"
          echo "terraform-args=$ARGS" >> $GITHUB_OUTPUT

  init-deployment:
    name: Initialize Terraform Deployment
    runs-on: ubuntu-latest
    needs: [safety-check, package-lambdas, setup-terraform, prepare-terraform-args]
    env:
      BASE_ENVIRONMENT: ${{ needs.safety-check.outputs.base-environment }}
      ACCOUNT_TYPE: ${{ needs.safety-check.outputs.account-type }}
      REGION_ABBREVIATION: ${{ needs.safety-check.outputs.region-abbreviation }}
      TF_ENVIRONMENT_DIRECTORY: ${{ needs.safety-check.outputs.tf-environment-directory }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Download lambda artifacts
        uses: actions/download-artifact@v4
        with:
          name: lambda-packages
          path: ${{ env.LAMBDA_ARTIFACT_PATH }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          # Note: You'll need to set up AWS credentials in GitHub secrets
          # The original script uses ALKS for credential management
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-session-token: ${{ secrets.AWS_SESSION_TOKEN }}
          aws-region: ${{ github.event.inputs.region || env.DEFAULT_REGION_NAME }}

      - name: Copy shared Terraform files
        run: |
          # Copy global shared variable tf file
          cp "./infrastructure/shared-variables.global.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-global.tf"
          
          # Copy data sources tf file
          cp "./infrastructure/data-sources.global.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-data-sources.tf"
          
          # Copy account-specific shared variable tf file
          cp "./infrastructure/shared-variables.${{ env.ACCOUNT_TYPE }}.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-account.tf"
          
          # Copy region-specific shared variable tf file
          cp "./infrastructure/shared-variables.${{ env.ACCOUNT_TYPE }}.${{ env.REGION_ABBREVIATION }}.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-region.tf"

      - name: Initialize Terraform
        working-directory: ${{ env.TF_ENVIRONMENT_DIRECTORY }}
        run: |
          TFSTATE_BUCKET_KEY="AIS-1.0/${{ github.event.inputs.environment }}/${{ github.event.inputs.component }}"
          TFSTATE_BUCKET_NAME="ais.${{ env.ACCOUNT_TYPE }}.${{ env.REGION_ABBREVIATION }}.infrastructure.tf.state"
          
          terraform init \
            -input=false \
            -get=true \
            -backend-config="region=${{ github.event.inputs.region || env.DEFAULT_REGION_NAME }}" \
            -backend-config="bucket=$TFSTATE_BUCKET_NAME" \
            -backend-config="key=$TFSTATE_BUCKET_KEY"

  plan:
    name: Terraform Plan
    runs-on: ubuntu-latest
    needs: [safety-check, init-deployment, prepare-terraform-args]
    if: contains(fromJson('["Default", "Plan", "Apply"]'), github.event.inputs.target)
    env:
      BASE_ENVIRONMENT: ${{ needs.safety-check.outputs.base-environment }}
      ACCOUNT_TYPE: ${{ needs.safety-check.outputs.account-type }}
      REGION_ABBREVIATION: ${{ needs.safety-check.outputs.region-abbreviation }}
      TF_ENVIRONMENT_DIRECTORY: ${{ needs.safety-check.outputs.tf-environment-directory }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Download lambda artifacts
        uses: actions/download-artifact@v4
        with:
          name: lambda-packages
          path: ${{ env.LAMBDA_ARTIFACT_PATH }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-session-token: ${{ secrets.AWS_SESSION_TOKEN }}
          aws-region: ${{ github.event.inputs.region || env.DEFAULT_REGION_NAME }}

      - name: Copy shared Terraform files
        run: |
          cp "./infrastructure/shared-variables.global.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-global.tf"
          cp "./infrastructure/data-sources.global.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-data-sources.tf"
          cp "./infrastructure/shared-variables.${{ env.ACCOUNT_TYPE }}.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-account.tf"
          cp "./infrastructure/shared-variables.${{ env.ACCOUNT_TYPE }}.${{ env.REGION_ABBREVIATION }}.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-region.tf"

      - name: Initialize Terraform
        working-directory: ${{ env.TF_ENVIRONMENT_DIRECTORY }}
        run: |
          TFSTATE_BUCKET_KEY="AIS-1.0/${{ github.event.inputs.environment }}/${{ github.event.inputs.component }}"
          TFSTATE_BUCKET_NAME="ais.${{ env.ACCOUNT_TYPE }}.${{ env.REGION_ABBREVIATION }}.infrastructure.tf.state"
          
          terraform init \
            -input=false \
            -get=true \
            -backend-config="region=${{ github.event.inputs.region || env.DEFAULT_REGION_NAME }}" \
            -backend-config="bucket=$TFSTATE_BUCKET_NAME" \
            -backend-config="key=$TFSTATE_BUCKET_KEY"

      - name: Terraform Plan
        working-directory: ${{ env.TF_ENVIRONMENT_DIRECTORY }}
        run: |
          terraform plan ${{ needs.prepare-terraform-args.outputs.terraform-args }} -out=plan

      - name: Upload plan artifact
        uses: actions/upload-artifact@v4
        with:
          name: terraform-plan
          path: ${{ env.TF_ENVIRONMENT_DIRECTORY }}/plan
          retention-days: 1

  apply:
    name: Terraform Apply
    runs-on: ubuntu-latest
    needs: [safety-check, plan, prepare-terraform-args]
    if: github.event.inputs.target == 'Apply'
    environment: 
      name: ${{ github.event.inputs.environment }}
    env:
      BASE_ENVIRONMENT: ${{ needs.safety-check.outputs.base-environment }}
      ACCOUNT_TYPE: ${{ needs.safety-check.outputs.account-type }}
      REGION_ABBREVIATION: ${{ needs.safety-check.outputs.region-abbreviation }}
      TF_ENVIRONMENT_DIRECTORY: ${{ needs.safety-check.outputs.tf-environment-directory }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Download lambda artifacts
        uses: actions/download-artifact@v4
        with:
          name: lambda-packages
          path: ${{ env.LAMBDA_ARTIFACT_PATH }}

      - name: Download plan artifact
        uses: actions/download-artifact@v4
        with:
          name: terraform-plan
          path: ${{ env.TF_ENVIRONMENT_DIRECTORY }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-session-token: ${{ secrets.AWS_SESSION_TOKEN }}
          aws-region: ${{ github.event.inputs.region || env.DEFAULT_REGION_NAME }}

      - name: Copy shared Terraform files
        run: |
          cp "./infrastructure/shared-variables.global.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-global.tf"
          cp "./infrastructure/data-sources.global.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-data-sources.tf"
          cp "./infrastructure/shared-variables.${{ env.ACCOUNT_TYPE }}.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-account.tf"
          cp "./infrastructure/shared-variables.${{ env.ACCOUNT_TYPE }}.${{ env.REGION_ABBREVIATION }}.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-region.tf"

      - name: Initialize Terraform
        working-directory: ${{ env.TF_ENVIRONMENT_DIRECTORY }}
        run: |
          TFSTATE_BUCKET_KEY="AIS-1.0/${{ github.event.inputs.environment }}/${{ github.event.inputs.component }}"
          TFSTATE_BUCKET_NAME="ais.${{ env.ACCOUNT_TYPE }}.${{ env.REGION_ABBREVIATION }}.infrastructure.tf.state"
          
          terraform init \
            -input=false \
            -get=true \
            -backend-config="region=${{ github.event.inputs.region || env.DEFAULT_REGION_NAME }}" \
            -backend-config="bucket=$TFSTATE_BUCKET_NAME" \
            -backend-config="key=$TFSTATE_BUCKET_KEY"

      - name: Terraform Apply
        working-directory: ${{ env.TF_ENVIRONMENT_DIRECTORY }}
        run: terraform apply plan

  destroy:
    name: Terraform Destroy
    runs-on: ubuntu-latest
    needs: [safety-check, prepare-terraform-args]
    if: github.event.inputs.target == 'Destroy'
    environment: 
      name: ${{ github.event.inputs.environment }}-destroy
    env:
      BASE_ENVIRONMENT: ${{ needs.safety-check.outputs.base-environment }}
      ACCOUNT_TYPE: ${{ needs.safety-check.outputs.account-type }}
      REGION_ABBREVIATION: ${{ needs.safety-check.outputs.region-abbreviation }}
      TF_ENVIRONMENT_DIRECTORY: ${{ needs.safety-check.outputs.tf-environment-directory }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-session-token: ${{ secrets.AWS_SESSION_TOKEN }}
          aws-region: ${{ github.event.inputs.region || env.DEFAULT_REGION_NAME }}

      - name: Copy shared Terraform files
        run: |
          cp "./infrastructure/shared-variables.global.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-global.tf"
          cp "./infrastructure/data-sources.global.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-data-sources.tf"
          cp "./infrastructure/shared-variables.${{ env.ACCOUNT_TYPE }}.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-account.tf"
          cp "./infrastructure/shared-variables.${{ env.ACCOUNT_TYPE }}.${{ env.REGION_ABBREVIATION }}.tf" "${{ env.TF_ENVIRONMENT_DIRECTORY }}/shared-variables-region.tf"

      - name: Initialize Terraform
        working-directory: ${{ env.TF_ENVIRONMENT_DIRECTORY }}
        run: |
          TFSTATE_BUCKET_KEY="AIS-1.0/${{ github.event.inputs.environment }}/${{ github.event.inputs.component }}"
          TFSTATE_BUCKET_NAME="ais.${{ env.ACCOUNT_TYPE }}.${{ env.REGION_ABBREVIATION }}.infrastructure.tf.state"
          
          terraform init \
            -input=false \
            -get=true \
            -backend-config="region=${{ github.event.inputs.region || env.DEFAULT_REGION_NAME }}" \
            -backend-config="bucket=$TFSTATE_BUCKET_NAME" \
            -backend-config="key=$TFSTATE_BUCKET_KEY"

      - name: Terraform Destroy
        working-directory: ${{ env.TF_ENVIRONMENT_DIRECTORY }}
        run: terraform destroy -auto-approve ${{ needs.prepare-terraform-args.outputs.terraform-args }}
