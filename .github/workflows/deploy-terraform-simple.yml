name: Deploy Terraform Component (Simple)

on:
  workflow_dispatch:
    inputs:
      target:
        description: 'Target task to run'
        required: true
        default: 'Default'
        type: choice
        options:
          - Default
          - Plan
          - Apply
          - Destroy
      environment:
        description: 'Environment name to deploy'
        required: true
        type: string
      component:
        description: 'Component name to deploy'
        required: true
        type: string
      account:
        description: 'AWS account'
        required: false
        default: 'awsaaianp'
        type: string
      region:
        description: 'AWS region'
        required: false
        default: 'us-east-1'
        type: string
      on_cluster:
        description: 'On cluster flag'
        required: false
        type: string
      profile_name:
        description: 'AWS profile name'
        required: false
        type: string

jobs:
  deploy:
    name: Deploy Infrastructure
    uses: ./.github/workflows/terraform-deploy.yml
    with:
      target: ${{ github.event.inputs.target }}
      environment: ${{ github.event.inputs.environment }}
      component: ${{ github.event.inputs.component }}
      account: ${{ github.event.inputs.account }}
      region: ${{ github.event.inputs.region }}
      on_cluster: ${{ github.event.inputs.on_cluster }}
      profile_name: ${{ github.event.inputs.profile_name }}
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_SESSION_TOKEN: ${{ secrets.AWS_SESSION_TOKEN }}
