name: Deploy Terraform Component

on:
  workflow_call:
    inputs:
      target:
        description: 'Target task to run'
        required: true
        type: string
      environment:
        description: 'Environment name to deploy'
        required: true
        type: string
      component:
        description: 'Component name to deploy'
        required: true
        type: string
      account:
        description: 'AWS account'
        required: false
        default: 'awsaaianp'
        type: string
      region:
        description: 'AWS region'
        required: false
        default: 'us-east-1'
        type: string
      on_cluster:
        description: 'On cluster flag'
        required: false
        type: string
      profile_name:
        description: 'AWS profile name'
        required: false
        type: string
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      AWS_SESSION_TOKEN:
        required: false

  workflow_dispatch:
    inputs:
      target:
        description: 'Target task to run'
        required: true
        default: 'Default'
        type: choice
        options:
          - Default
          - Plan
          - Apply
          - Destroy
      environment:
        description: 'Environment name to deploy'
        required: true
        type: string
      component:
        description: 'Component name to deploy'
        required: true
        type: string
      account:
        description: 'AWS account'
        required: false
        default: 'awsaaianp'
        type: string
      region:
        description: 'AWS region'
        required: false
        default: 'us-east-1'
        type: string
      on_cluster:
        description: 'On cluster flag'
        required: false
        type: string
      profile_name:
        description: 'AWS profile name'
        required: false
        type: string

env:
  APPLICATION_ABBREVIATION: "ais10"
  TERRAFORM_VERSION: "0.13.7"
  DEFAULT_REGION_NAME: "us-east-1"
  ARTIFACT_PATH: "./artifacts"
  LAMBDA_SOURCE_PATH: "./infrastructure/lambda"
  LAMBDA_ARTIFACT_PATH: "./artifacts/lambda"
  MYSQL_VERSION_SUFFIX: "57"

jobs:
  deploy:
    name: Deploy Terraform Component
    runs-on: ubuntu-latest
    outputs:
      base-environment: ${{ steps.environment-parser.outputs.base-environment }}
      account-type: ${{ steps.environment-parser.outputs.account-type }}
      region-abbreviation: ${{ steps.environment-parser.outputs.region-abbreviation }}
      tf-environment-directory: ${{ steps.environment-parser.outputs.tf-environment-directory }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Parse Environment
        id: environment-parser
        uses: ./.github/actions/parse-environment
        with:
          environment: ${{ inputs.environment }}
          component: ${{ inputs.component }}
          region: ${{ inputs.region || env.DEFAULT_REGION_NAME }}
          account: ${{ inputs.account || 'awsaaianp' }}

      - name: Validate Inputs
        uses: ./.github/actions/validate-inputs
        with:
          environment: ${{ inputs.environment }}
          component: ${{ inputs.component }}
          region: ${{ inputs.region || env.DEFAULT_REGION_NAME }}
          tf-environment-directory: ${{ steps.environment-parser.outputs.tf-environment-directory }}
          account-type: ${{ steps.environment-parser.outputs.account-type }}
          region-abbreviation: ${{ steps.environment-parser.outputs.region-abbreviation }}

      - name: Load Component Configuration
        id: load-config
        uses: ./.github/actions/load-component-config
        with:
          base-environment: ${{ steps.environment-parser.outputs.base-environment }}
          component: ${{ inputs.component }}
          lambda-source-path: ${{ env.LAMBDA_SOURCE_PATH }}

      - name: Setup Node.js
        if: steps.load-config.outputs.has-lambdas == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'npm'
          cache-dependency-path: '**/package.json'

      - name: Package Lambda Functions
        if: steps.load-config.outputs.has-lambdas == 'true'
        uses: ./.github/actions/package-lambdas
        with:
          lambda-folders: ${{ steps.load-config.outputs.lambda-folders }}
          lambda-source-path: ${{ env.LAMBDA_SOURCE_PATH }}
          lambda-artifact-path: ${{ env.LAMBDA_ARTIFACT_PATH }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Prepare Terraform Arguments
        id: terraform-args
        uses: ./.github/actions/prepare-terraform-args
        with:
          config-file: ${{ steps.load-config.outputs.config-file }}
          environment: ${{ inputs.environment }}
          component: ${{ inputs.component }}
          region: ${{ inputs.region || env.DEFAULT_REGION_NAME }}
          build-number: ${{ github.run_number }}
          launched-by: ${{ github.actor }}
          on-cluster: ${{ inputs.on_cluster }}
          lambda-artifact-path: ${{ env.LAMBDA_ARTIFACT_PATH }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-session-token: ${{ secrets.AWS_SESSION_TOKEN }}
          aws-region: ${{ inputs.region || env.DEFAULT_REGION_NAME }}

      - name: Initialize Terraform
        uses: ./.github/actions/terraform-init
        with:
          tf-environment-directory: ${{ steps.environment-parser.outputs.tf-environment-directory }}
          account-type: ${{ steps.environment-parser.outputs.account-type }}
          region-abbreviation: ${{ steps.environment-parser.outputs.region-abbreviation }}
          environment: ${{ inputs.environment }}
          component: ${{ inputs.component }}
          region: ${{ inputs.region || env.DEFAULT_REGION_NAME }}

      - name: Terraform Plan
        if: contains(fromJson('["Default", "Plan", "Apply"]'), inputs.target)
        uses: ./.github/actions/terraform-plan
        with:
          tf-environment-directory: ${{ steps.environment-parser.outputs.tf-environment-directory }}
          terraform-args: ${{ steps.terraform-args.outputs.terraform-args }}

      - name: Terraform Apply
        if: inputs.target == 'Apply'
        uses: ./.github/actions/terraform-apply
        with:
          tf-environment-directory: ${{ steps.environment-parser.outputs.tf-environment-directory }}
          environment: ${{ inputs.environment }}
        environment: 
          name: ${{ inputs.environment }}
          url: ${{ steps.terraform-plan.outputs.plan-url || '' }}

      - name: Terraform Destroy
        if: inputs.target == 'Destroy'
        uses: ./.github/actions/terraform-destroy
        with:
          tf-environment-directory: ${{ steps.environment-parser.outputs.tf-environment-directory }}
          terraform-args: ${{ steps.terraform-args.outputs.terraform-args }}
          environment: ${{ inputs.environment }}
        environment: 
          name: ${{ inputs.environment }}-destroy
