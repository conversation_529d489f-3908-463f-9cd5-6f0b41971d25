name: 'Terraform Plan'
description: 'Create Terraform execution plan'
inputs:
  tf-environment-directory:
    description: 'Terraform environment directory'
    required: true
  terraform-args:
    description: 'Terraform arguments'
    required: true

runs:
  using: "composite"
  steps:
    - name: Create Terraform Plan
      shell: bash
      working-directory: ${{ inputs.tf-environment-directory }}
      run: |
        echo "📋 Creating Terraform execution plan..."
        echo "Arguments: ${{ inputs.terraform-args }}"
        
        terraform plan ${{ inputs.terraform-args }} -out=plan
        
        echo "✅ Terraform plan created successfully"

    - name: Upload Plan Artifact
      uses: actions/upload-artifact@v4
      with:
        name: terraform-plan-${{ github.run_id }}
        path: ${{ inputs.tf-environment-directory }}/plan
        retention-days: 1

    - name: Show Plan Summary
      shell: bash
      working-directory: ${{ inputs.tf-environment-directory }}
      run: |
        echo "📊 Plan Summary:"
        terraform show -no-color plan | head -20
        echo ""
        echo "📄 Full plan saved as artifact: terraform-plan-${{ github.run_id }}"
