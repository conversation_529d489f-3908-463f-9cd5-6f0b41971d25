name: 'Validate Inputs'
description: 'Validate deployment inputs and verify required paths exist'
inputs:
  environment:
    description: 'Environment name'
    required: true
  component:
    description: 'Component name'
    required: true
  region:
    description: 'AWS region'
    required: true
  tf-environment-directory:
    description: 'Terraform environment directory'
    required: true
  account-type:
    description: 'Account type'
    required: true
  region-abbreviation:
    description: 'Region abbreviation'
    required: true

runs:
  using: "composite"
  steps:
    - name: Validate Required Inputs
      shell: bash
      run: |
        # Verify required arguments
        if [[ -z "${{ inputs.environment }}" ]]; then
          echo "❌ Error: Must specify an environment name to be deployed with Terraform"
          exit 1
        fi
        
        if [[ -z "${{ inputs.component }}" ]]; then
          echo "❌ Error: Must specify a component name for the environment to be deployed with Terraform"
          exit 1
        fi
        
        if [[ -z "${{ inputs.region }}" ]]; then
          echo "❌ Error: Must specify a region name for the environment to be deployed with Terraform"
          exit 1
        fi
        
        echo "✅ Required inputs validated"

    - name: Verify Paths Exist
      shell: bash
      run: |
        # Verify environment/component directory exists
        if [[ ! -d "${{ inputs.tf-environment-directory }}" ]]; then
          echo "❌ Error: Invalid combination of environment and component: can't find path ${{ inputs.tf-environment-directory }}"
          exit 1
        fi
        
        # Verify shared variable files exist
        SHARED_VAR_ACCOUNT="./infrastructure/shared-variables.${{ inputs.account-type }}.tf"
        SHARED_VAR_REGION="./infrastructure/shared-variables.${{ inputs.account-type }}.${{ inputs.region-abbreviation }}.tf"
        
        if [[ ! -f "$SHARED_VAR_ACCOUNT" ]]; then
          echo "❌ Error: Unable to find shared variable file for account at $SHARED_VAR_ACCOUNT"
          exit 1
        fi
        
        if [[ ! -f "$SHARED_VAR_REGION" ]]; then
          echo "❌ Error: Unable to find shared variable file for region at $SHARED_VAR_REGION"
          exit 1
        fi
        
        echo "✅ All required paths exist"
        echo "  Environment/Component directory: ${{ inputs.tf-environment-directory }}"
        echo "  Account shared variables: $SHARED_VAR_ACCOUNT"
        echo "  Region shared variables: $SHARED_VAR_REGION"
