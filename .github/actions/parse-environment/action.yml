name: 'Parse Environment'
description: 'Parse environment parameters and determine base environment, account type, and region abbreviation'
inputs:
  environment:
    description: 'Environment name'
    required: true
  component:
    description: 'Component name'
    required: true
  region:
    description: 'AWS region'
    required: true
  account:
    description: 'AWS account'
    required: true
outputs:
  base-environment:
    description: 'Base environment name'
    value: ${{ steps.parse.outputs.base-environment }}
  account-type:
    description: 'Account type (prod/nonprod)'
    value: ${{ steps.parse.outputs.account-type }}
  region-abbreviation:
    description: 'Region abbreviation'
    value: ${{ steps.parse.outputs.region-abbreviation }}
  tf-environment-directory:
    description: 'Terraform environment directory path'
    value: ${{ steps.parse.outputs.tf-environment-directory }}

runs:
  using: "composite"
  steps:
    - name: Parse Environment Parameters
      id: parse
      shell: bash
      run: |
        # Determine base environment
        ENVIRONMENT="${{ inputs.environment }}"
        if [[ "$ENVIRONMENT" == scratch* ]]; then
          BASE_ENV="scratch"
        elif [[ "$ENVIRONMENT" == uat* ]]; then
          BASE_ENV="uat"
        elif [[ "$ENVIRONMENT" == qamain* ]]; then
          BASE_ENV="qamain"
        elif [[ "$ENVIRONMENT" == qarapid* ]]; then
          BASE_ENV="qarapid"
        elif [[ "$ENVIRONMENT" == homenet* ]]; then
          BASE_ENV="homenet"
        elif [[ "$ENVIRONMENT" == production* ]]; then
          BASE_ENV="production"
        elif [[ "$ENVIRONMENT" == nonprod* ]]; then
          BASE_ENV="nonprod"
        else
          BASE_ENV="$ENVIRONMENT"
        fi
        
        # Determine account type
        if [[ "$BASE_ENV" == "production" ]]; then
          ACCOUNT_TYPE="prod"
        else
          ACCOUNT_TYPE="nonprod"
        fi
        
        # Calculate region abbreviation (us-east-1 => ue1)
        REGION="${{ inputs.region }}"
        REGION_PARTS=(${REGION//-/ })
        REGION_ABBREV="${REGION_PARTS[0]:0:1}${REGION_PARTS[1]:0:1}${REGION_PARTS[2]}"
        
        # Set paths
        TF_ENV_DIR="./infrastructure/environments/${BASE_ENV}/${{ inputs.component }}"
        
        echo "base-environment=$BASE_ENV" >> $GITHUB_OUTPUT
        echo "account-type=$ACCOUNT_TYPE" >> $GITHUB_OUTPUT
        echo "region-abbreviation=$REGION_ABBREV" >> $GITHUB_OUTPUT
        echo "tf-environment-directory=$TF_ENV_DIR" >> $GITHUB_OUTPUT
        
        echo "Parsed Environment:"
        echo "  Base Environment: $BASE_ENV"
        echo "  Account Type: $ACCOUNT_TYPE"
        echo "  Region Abbreviation: $REGION_ABBREV"
        echo "  TF Directory: $TF_ENV_DIR"
