name: 'Terraform Apply'
description: 'Apply Terraform plan with environment protection'
inputs:
  tf-environment-directory:
    description: 'Terraform environment directory'
    required: true
  environment:
    description: 'Environment name for protection'
    required: true

runs:
  using: "composite"
  steps:
    - name: Download Plan Artifact
      uses: actions/download-artifact@v4
      with:
        name: terraform-plan-${{ github.run_id }}
        path: ${{ inputs.tf-environment-directory }}

    - name: Apply Terraform Plan
      shell: bash
      working-directory: ${{ inputs.tf-environment-directory }}
      run: |
        echo "🚀 Applying Terraform plan for environment: ${{ inputs.environment }}"
        
        # Verify plan file exists
        if [[ ! -f "plan" ]]; then
          echo "❌ Error: Plan file not found. Please run terraform plan first."
          exit 1
        fi
        
        terraform apply plan
        
        echo "✅ Terraform apply completed successfully"

    - name: Show Apply Results
      shell: bash
      working-directory: ${{ inputs.tf-environment-directory }}
      run: |
        echo "📊 Apply Results:"
        echo "Environment: ${{ inputs.environment }}"
        echo "Status: Applied successfully"
        echo "Timestamp: $(date -u +%Y-%m-%dT%H:%M:%SZ)"
