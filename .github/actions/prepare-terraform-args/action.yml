name: 'Prepare Terraform Arguments'
description: 'Build Terraform command-line arguments from global parameters and component configuration'
inputs:
  config-file:
    description: 'Path to component configuration file'
    required: false
  environment:
    description: 'Environment name'
    required: true
  component:
    description: 'Component name'
    required: true
  region:
    description: 'AWS region'
    required: true
  build-number:
    description: 'Build number'
    required: true
  launched-by:
    description: 'Who launched this deployment'
    required: true
  on-cluster:
    description: 'On cluster flag'
    required: false
  lambda-artifact-path:
    description: 'Path to lambda artifacts'
    required: true
outputs:
  terraform-args:
    description: 'Complete Terraform arguments string'
    value: ${{ steps.build-args.outputs.terraform-args }}

runs:
  using: "composite"
  steps:
    - name: Install jq
      shell: bash
      run: |
        if ! command -v jq &> /dev/null; then
          sudo apt-get update && sudo apt-get install -y jq
        fi

    - name: Get Component Arguments
      id: component-args
      shell: bash
      run: |
        CONFIG_FILE="${{ inputs.config-file }}"
        
        if [[ -z "$CONFIG_FILE" || ! -f "$CONFIG_FILE" ]]; then
          echo "ℹ️  No config file provided - proceeding with global arguments only"
          echo "component-args=" >> $GITHUB_OUTPUT
          exit 0
        fi
        
        echo "🔧 Processing component configuration: $CONFIG_FILE"
        
        ARGS=""
        
        # Process each config item
        while IFS= read -r line; do
          if [[ -n "$line" ]]; then
            terraform_var=$(echo "$line" | jq -r '.TerraformVariableName')
            cake_arg=$(echo "$line" | jq -r '.CakeArgumentName // ""')
            default_val=$(echo "$line" | jq -r '.CakeArgumentDefaultValue // ""')
            is_optional=$(echo "$line" | jq -r '.IsOptional // false')
            lambda_folder=$(echo "$line" | jq -r '.LambdaFolderName // ""')
            
            param_value=""
            
            # Handle lambda parameters specially
            if [[ -n "$lambda_folder" && "$lambda_folder" != "null" ]]; then
              param_value="${{ inputs.lambda-artifact-path }}/${lambda_folder}.zip"
              echo "📦 Lambda parameter: ${terraform_var} = ${param_value}"
            else
              # Try to get value from environment variable, then check for default
              if [[ -n "$cake_arg" && "$cake_arg" != "null" ]]; then
                # Use indirect variable reference to get environment variable value
                param_value="${!cake_arg:-}"
                if [[ -z "$param_value" && -n "$default_val" && "$default_val" != "null" ]]; then
                  param_value="$default_val"
                fi
                if [[ -n "$param_value" ]]; then
                  echo "⚙️  Parameter: ${terraform_var} = ${param_value}"
                fi
              fi
            fi
            
            # Skip optional parameters that are empty
            if [[ "$is_optional" == "true" && -z "$param_value" ]]; then
              continue
            fi
            
            if [[ -n "$param_value" ]]; then
              ARGS="$ARGS -var \"${terraform_var}=${param_value}\""
            fi
          fi
        done < <(jq -c '.[]' "$CONFIG_FILE" 2>/dev/null || echo "")
        
        echo "component-args=$ARGS" >> $GITHUB_OUTPUT

    - name: Build Final Arguments
      id: build-args
      shell: bash
      run: |
        # Build global terraform arguments
        ARGS=""
        ARGS="$ARGS -var \"region=${{ inputs.region }}\""
        ARGS="$ARGS -var \"build_number=${{ inputs.build-number }}\""
        ARGS="$ARGS -var \"environment=${{ inputs.environment }}\""
        ARGS="$ARGS -var \"launched_by=${{ inputs.launched-by }}\""
        ARGS="$ARGS -var \"launched_on=$(date -u +%Y-%m-%dT%H:%M:%SZ)\""
        ARGS="$ARGS -var \"component=${{ inputs.component }}\""
        
        # Default Slack channel - you may want to make this configurable
        ARGS="$ARGS -var \"slack_contact=+general\""
        
        if [[ -n "${{ inputs.on-cluster }}" ]]; then
          ARGS="$ARGS -var \"onCluster=${{ inputs.on-cluster }}\""
        fi
        
        # Add component-specific arguments
        COMPONENT_ARGS="${{ steps.component-args.outputs.component-args }}"
        if [[ -n "$COMPONENT_ARGS" ]]; then
          ARGS="$ARGS $COMPONENT_ARGS"
        fi
        
        echo "🏗️  Final Terraform arguments:"
        echo "$ARGS"
        echo "terraform-args=$ARGS" >> $GITHUB_OUTPUT
