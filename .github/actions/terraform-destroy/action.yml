name: 'Terraform Destroy'
description: 'Destroy Terraform infrastructure with safety checks'
inputs:
  tf-environment-directory:
    description: 'Terraform environment directory'
    required: true
  terraform-args:
    description: 'Terraform arguments'
    required: true
  environment:
    description: 'Environment name for safety'
    required: true

runs:
  using: "composite"
  steps:
    - name: Safety Check for Destroy
      shell: bash
      run: |
        echo "⚠️  WARNING: About to destroy infrastructure for environment: ${{ inputs.environment }}"
        echo "Directory: ${{ inputs.tf-environment-directory }}"
        echo "Arguments: ${{ inputs.terraform-args }}"
        
        # Add additional safety checks here if needed
        if [[ "${{ inputs.environment }}" == production* ]]; then
          echo "🔒 Production environment detected - ensure proper approvals are in place"
        fi

    - name: Destroy Terraform Infrastructure
      shell: bash
      working-directory: ${{ inputs.tf-environment-directory }}
      run: |
        echo "💥 Destroying Terraform infrastructure..."
        
        terraform destroy -auto-approve ${{ inputs.terraform-args }}
        
        echo "✅ Terraform destroy completed"

    - name: Confirm Destruction
      shell: bash
      working-directory: ${{ inputs.tf-environment-directory }}
      run: |
        echo "🗑️  Destruction Results:"
        echo "Environment: ${{ inputs.environment }}"
        echo "Status: Destroyed successfully"
        echo "Timestamp: $(date -u +%Y-%m-%dT%H:%M:%SZ)"
        echo ""
        echo "ℹ️  All infrastructure for this component has been removed."
