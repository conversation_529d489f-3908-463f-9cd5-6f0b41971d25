name: 'Initialize Terraform'
description: 'Initialize Terraform with shared files and backend configuration'
inputs:
  tf-environment-directory:
    description: 'Terraform environment directory'
    required: true
  account-type:
    description: 'Account type (prod/nonprod)'
    required: true
  region-abbreviation:
    description: 'Region abbreviation'
    required: true
  environment:
    description: 'Environment name'
    required: true
  component:
    description: 'Component name'
    required: true
  region:
    description: 'AWS region'
    required: true

runs:
  using: "composite"
  steps:
    - name: Download Lambda Artifacts
      uses: actions/download-artifact@v4
      with:
        name: lambda-packages-${{ github.run_id }}
        path: ./artifacts/lambda
      continue-on-error: true

    - name: Copy Shared Terraform Files
      shell: bash
      run: |
        echo "📂 Copying shared Terraform files to ${{ inputs.tf-environment-directory }}"
        
        # Copy global shared variable tf file
        cp "./infrastructure/shared-variables.global.tf" "${{ inputs.tf-environment-directory }}/shared-variables-global.tf"
        echo "✅ Copied global shared variables"
        
        # Copy data sources tf file
        cp "./infrastructure/data-sources.global.tf" "${{ inputs.tf-environment-directory }}/shared-data-sources.tf"
        echo "✅ Copied global data sources"
        
        # Copy account-specific shared variable tf file
        cp "./infrastructure/shared-variables.${{ inputs.account-type }}.tf" "${{ inputs.tf-environment-directory }}/shared-variables-account.tf"
        echo "✅ Copied account-specific shared variables (${{ inputs.account-type }})"
        
        # Copy region-specific shared variable tf file
        cp "./infrastructure/shared-variables.${{ inputs.account-type }}.${{ inputs.region-abbreviation }}.tf" "${{ inputs.tf-environment-directory }}/shared-variables-region.tf"
        echo "✅ Copied region-specific shared variables (${{ inputs.account-type }}.${{ inputs.region-abbreviation }})"

    - name: Initialize Terraform
      shell: bash
      working-directory: ${{ inputs.tf-environment-directory }}
      run: |
        echo "🚀 Initializing Terraform in ${{ inputs.tf-environment-directory }}"
        
        TFSTATE_BUCKET_KEY="AIS-1.0/${{ inputs.environment }}/${{ inputs.component }}"
        TFSTATE_BUCKET_NAME="ais.${{ inputs.account-type }}.${{ inputs.region-abbreviation }}.infrastructure.tf.state"
        
        echo "📦 State configuration:"
        echo "  Bucket: $TFSTATE_BUCKET_NAME"
        echo "  Key: $TFSTATE_BUCKET_KEY"
        echo "  Region: ${{ inputs.region }}"
        
        terraform init \
          -input=false \
          -get=true \
          -backend-config="region=${{ inputs.region }}" \
          -backend-config="bucket=$TFSTATE_BUCKET_NAME" \
          -backend-config="key=$TFSTATE_BUCKET_KEY"
        
        echo "✅ Terraform initialization completed"

    - name: Verify Terraform Version
      shell: bash
      working-directory: ${{ inputs.tf-environment-directory }}
      run: |
        echo "🔍 Terraform version information:"
        terraform --version
