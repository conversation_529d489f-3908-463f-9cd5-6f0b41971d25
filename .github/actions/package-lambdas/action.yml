name: 'Package Lambda Functions'
description: 'Package Lambda functions into zip files for deployment'
inputs:
  lambda-folders:
    description: 'Newline-separated list of lambda folder names'
    required: true
  lambda-source-path:
    description: 'Path to lambda source directory'
    required: true
  lambda-artifact-path:
    description: 'Path to lambda artifact directory'
    required: true

runs:
  using: "composite"
  steps:
    - name: Create Artifact Directories
      shell: bash
      run: |
        mkdir -p ${{ inputs.lambda-artifact-path }}
        rm -rf ${{ inputs.lambda-artifact-path }}/*
        echo "📁 Created artifact directory: ${{ inputs.lambda-artifact-path }}"

    - name: Package Lambda Functions
      shell: bash
      run: |
        echo "📦 Packaging lambda functions..."
        LAMBDA_FOLDERS='${{ inputs.lambda-folders }}'
        
        if [[ -z "$LAMBDA_FOLDERS" ]]; then
          echo "ℹ️  No lambda functions to package"
          exit 0
        fi
        
        while IFS= read -r lambda_folder; do
          if [[ -n "$lambda_folder" ]]; then
            echo "📦 Packaging lambda: $lambda_folder"
            
            lambda_source_path="${{ inputs.lambda-source-path }}/$lambda_folder"
            lambda_artifact_file="${{ inputs.lambda-artifact-path }}/${lambda_folder}.zip"
            
            if [[ ! -d "$lambda_source_path" ]]; then
              echo "❌ Error: Lambda source directory not found: $lambda_source_path"
              exit 1
            fi
            
            # Check if package.json exists and install dependencies
            if [[ -f "$lambda_source_path/package.json" ]]; then
              echo "📋 Installing npm dependencies for $lambda_folder"
              cd "$lambda_source_path"
              npm ci --production --silent
              cd - > /dev/null
            fi
            
            # Create zip file
            echo "🗜️  Creating zip file: $lambda_artifact_file"
            cd "$lambda_source_path"
            zip -r "$lambda_artifact_file" . -x "*.git*" "*.md" "*.txt" "test/*" "tests/*" "spec/*" "__tests__/*" > /dev/null
            cd - > /dev/null
            
            # Verify zip file was created
            if [[ -f "$lambda_artifact_file" ]]; then
              file_size=$(du -h "$lambda_artifact_file" | cut -f1)
              echo "✅ Successfully packaged $lambda_folder (Size: $file_size)"
            else
              echo "❌ Error: Failed to create zip file for $lambda_folder"
              exit 1
            fi
          fi
        done <<< "$LAMBDA_FOLDERS"
        
        echo "✅ All lambda functions packaged successfully"

    - name: Upload Lambda Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: lambda-packages-${{ github.run_id }}
        path: ${{ inputs.lambda-artifact-path }}
        retention-days: 1
