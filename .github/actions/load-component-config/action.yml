name: 'Load Component Configuration'
description: 'Load and validate component configuration from JSON files'
inputs:
  base-environment:
    description: 'Base environment name'
    required: true
  component:
    description: 'Component name'
    required: true
  lambda-source-path:
    description: 'Lambda source path'
    required: true
outputs:
  config-file:
    description: 'Path to configuration file found'
    value: ${{ steps.find-config.outputs.config-file }}
  has-lambdas:
    description: 'Whether component has lambda functions'
    value: ${{ steps.find-config.outputs.has-lambdas }}
  lambda-folders:
    description: 'List of lambda folder names'
    value: ${{ steps.find-config.outputs.lambda-folders }}

runs:
  using: "composite"
  steps:
    - name: Install jq
      shell: bash
      run: |
        if ! command -v jq &> /dev/null; then
          sudo apt-get update && sudo apt-get install -y jq
        fi

    - name: Find Configuration File
      id: find-config
      shell: bash
      run: |
        # Define prioritized config files (most specific first)
        COMPONENT_FOLDER_PATH="./infrastructure/environments/${{ inputs.base-environment }}/${{ inputs.component }}"
        CONFIG_FOLDER_PATH="./infrastructure/configs/components"
        
        CONFIG_FILES=(
          "${COMPONENT_FOLDER_PATH}/config.json"
          "${CONFIG_FOLDER_PATH}/${{ inputs.component }}.json"
        )
        
        CONFIG_FILE=""
        for config_file in "${CONFIG_FILES[@]}"; do
          if [[ -f "$config_file" ]]; then
            CONFIG_FILE="$config_file"
            break
          fi
        done
        
        if [[ -n "$CONFIG_FILE" ]]; then
          echo "📄 Found config file: $CONFIG_FILE"
          echo "config-file=$CONFIG_FILE" >> $GITHUB_OUTPUT
        else
          echo "ℹ️  No config file found - component has no specific configuration parameters"
          echo "config-file=" >> $GITHUB_OUTPUT
          echo "has-lambdas=false" >> $GITHUB_OUTPUT
          echo "lambda-folders=" >> $GITHUB_OUTPUT
          exit 0
        fi
        
        # Validate JSON format
        if ! jq empty "$CONFIG_FILE" 2>/dev/null; then
          echo "❌ Error: Invalid JSON format in config file: $CONFIG_FILE"
          exit 1
        fi
        echo "✅ JSON validation passed for: $CONFIG_FILE"
        
        # Get lambda folder names
        LAMBDA_FOLDERS=$(jq -r '.[] | select(.LambdaFolderName != null and .LambdaFolderName != "") | .LambdaFolderName' "$CONFIG_FILE" 2>/dev/null || true)
        
        if [[ -n "$LAMBDA_FOLDERS" ]]; then
          echo "has-lambdas=true" >> $GITHUB_OUTPUT
          echo "lambda-folders<<EOF" >> $GITHUB_OUTPUT
          echo "$LAMBDA_FOLDERS" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
          echo "🔍 Found lambda functions:"
          echo "$LAMBDA_FOLDERS" | while read -r folder; do
            [[ -n "$folder" ]] && echo "  - $folder"
          done
        else
          echo "has-lambdas=false" >> $GITHUB_OUTPUT
          echo "lambda-folders=" >> $GITHUB_OUTPUT
        fi

    - name: Validate Lambda Directories
      if: steps.find-config.outputs.has-lambdas == 'true'
      shell: bash
      run: |
        CONFIG_FILE="${{ steps.find-config.outputs.config-file }}"
        LAMBDA_FOLDERS="${{ steps.find-config.outputs.lambda-folders }}"
        VALIDATION_ERRORS=()
        
        # Check each lambda parameter
        while IFS= read -r line; do
          if [[ -n "$line" ]]; then
            lambda_folder=$(echo "$line" | jq -r '.LambdaFolderName')
            terraform_var=$(echo "$line" | jq -r '.TerraformVariableName')
            
            if [[ -n "$lambda_folder" && "$lambda_folder" != "null" ]]; then
              lambda_path="${{ inputs.lambda-source-path }}/${lambda_folder}"
              if [[ ! -d "$lambda_path" ]]; then
                VALIDATION_ERRORS+=("Component ${{ inputs.component }} failed validation due to missing Lambda source for ${terraform_var} at ${lambda_path}")
              else
                echo "✅ Lambda directory exists: $lambda_path"
              fi
            fi
          fi
        done < <(jq -c '.[] | select(.LambdaFolderName != null and .LambdaFolderName != "")' "$CONFIG_FILE" 2>/dev/null || echo "")
        
        # Report validation errors if any
        if [[ ${#VALIDATION_ERRORS[@]} -gt 0 ]]; then
          echo "❌ Can't proceed due to validation errors that need resolved:"
          printf '%s\n' "${VALIDATION_ERRORS[@]}"
          exit 1
        fi
        
        echo "✅ Lambda directory validation passed"

    - name: Validate Parameter Values
      if: steps.find-config.outputs.config-file != ''
      shell: bash
      run: |
        CONFIG_FILE="${{ steps.find-config.outputs.config-file }}"
        VALIDATION_ERRORS=()
        
        # Process each non-lambda config item for validation
        while IFS= read -r line; do
          if [[ -n "$line" ]]; then
            terraform_var=$(echo "$line" | jq -r '.TerraformVariableName')
            cake_arg=$(echo "$line" | jq -r '.CakeArgumentName // ""')
            default_val=$(echo "$line" | jq -r '.CakeArgumentDefaultValue // ""')
            is_optional=$(echo "$line" | jq -r '.IsOptional // false')
            validation_regex=$(echo "$line" | jq -r '.ValidationRegex // ""')
            validation_text=$(echo "$line" | jq -r '.ValidationFailedText // "N/A"')
            
            # Skip if no validation regex provided
            if [[ -z "$validation_regex" || "$validation_regex" == "null" ]]; then
              continue
            fi
            
            # Get parameter value from environment variable
            param_value=""
            if [[ -n "$cake_arg" && "$cake_arg" != "null" ]]; then
              # Use indirect variable reference to get environment variable value
              param_value="${!cake_arg:-}"
              if [[ -z "$param_value" && -n "$default_val" && "$default_val" != "null" ]]; then
                param_value="$default_val"
              fi
            fi
            
            # Skip validation for optional empty parameters
            if [[ "$is_optional" == "true" && -z "$param_value" ]]; then
              continue
            fi
            
            # Validate against regex
            if [[ -n "$param_value" ]] && ! echo "$param_value" | grep -qE "$validation_regex"; then
              VALIDATION_ERRORS+=("Component ${{ inputs.component }} failed validation for argument ${cake_arg}: ${validation_text}")
            fi
          fi
        done < <(jq -c '.[] | select(.LambdaFolderName == null or .LambdaFolderName == "")' "$CONFIG_FILE" 2>/dev/null || echo "")
        
        # Report validation errors if any
        if [[ ${#VALIDATION_ERRORS[@]} -gt 0 ]]; then
          echo "❌ Can't get Terraform arguments due to validation errors that need resolved:"
          printf '%s\n' "${VALIDATION_ERRORS[@]}"
          exit 1
        fi
        
        echo "✅ Parameter validation passed"
