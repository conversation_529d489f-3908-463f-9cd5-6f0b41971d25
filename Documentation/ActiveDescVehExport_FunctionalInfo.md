# ActiveDescVehExport Class - Functional Information

## Overview
The `ActiveDescVehExport` class is a specialized component of the AISRIS system responsible for extracting active vehicle descriptions from the database, formatting the data into CSV files, compressing these files, and distributing them to stakeholders via email. This class extends the `BaseAction` class and provides methods for data extraction, file handling, and logging.

## Class Structure

### Constructor
```php
function ActiveDescVehExport($logger=null, $sql=null, $tpl=null, $AisDao=null)
```
- **Purpose**: Initializes the class with necessary dependencies
- **Parameters**:
  - `$logger`: Logger instance for recording activities
  - `$sql`: SQL connection instance for database operations
  - `$tpl`: Template engine instance
  - `$AisDao`: Data Access Object for AISRIS data
- **Actions**:
  - Sets up specialized logging configurations for the export process
  - Configures logger levels and instances
  - Sets up the Geography component with appropriate logger

### Primary Methods

#### createActiveDescVehFile()
```php
function createActiveDescVehFile()
```
- **Purpose**: Creates a CSV file of active described vehicles and compresses it
- **Process Flow**:
  1. Retrieves all active described vehicles from the database using `$this->AisDao->getAllActiveDescVehicles()`
  2. Changes directory to the configured save location
  3. Creates a CSV file named "ActiveDescribedVehicles_YYYY-MM-DD.csv" 
  4. Writes vehicle data to the CSV using semicolon separators and double quote enclosures
  5. Creates a ZIP archive containing the CSV file
  6. Cleans up the temporary CSV file after successful compression
  7. Logs the operation status
- **Return Value**: Returns the ZIP filename if successful, or `false` if the operation fails

#### createDataOneActiveDescVehFile()
```php
function createDataOneActiveDescVehFile()
```
- **Purpose**: Creates a specialized CSV file for DataOne integration
- **Process Flow**:
  1. Retrieves all active described vehicles with Vehicle Group information using `$this->AisDao->getAllDataOneActiveDescVehiclesPlusVehicleGroups()`
  2. Changes directory to the configured save location
  3. Creates a CSV file named "AIS_VEH_MAP_DataOne.csv"
  4. Writes vehicle data with headers to CSV using comma separators
  5. Creates a ZIP archive named "AIS_VEH_MAP_DataOne.zip"
  6. Cleans up the temporary CSV file
  7. Logs the operation status
- **Return Value**: Returns the ZIP filename if successful, or `false` if the operation fails

#### sendByFTPtoDataOne($full_filename)
```php
function sendByFTPtoDataOne($full_filename)
```
- **Purpose**: Transmits files to DataOne via FTP
- **Parameters**:
  - `$full_filename`: The name of the file to transmit
- **Process Flow**:
  1. Establishes an FTP connection using configured credentials
  2. Authenticates to the FTP server
  3. Enables passive mode for reliable transfers
  4. Changes to the configured subdirectory (if applicable)
  5. Uploads the file to the FTP server
  6. Closes the FTP connection
  7. Logs success or failure
- **Return Value**: Returns `true` if the file was successfully transmitted, `false` otherwise

#### emailFile($FileName)
```php
function emailFile($FileName)
```
- **Purpose**: Emails the generated data file to configured recipients
- **Parameters**:
  - `$FileName`: The name of the file to email
- **Process Flow**:
  1. Retrieves recipient list from configuration
  2. For each recipient, creates a PHPMailer instance
  3. Sets up email parameters (from, to, subject, etc.)
  4. Attaches the vehicle data ZIP file
  5. Sends the email
  6. Logs success or failure
  7. Cleans up mail resources
- **Return Value**: Returns `true` if emails were successfully sent to all recipients, `false` otherwise

#### logAndUpdateStatus($msg)
```php
function logAndUpdateStatus($msg)
```
- **Purpose**: Centralized logging method for consistent status updates
- **Parameters**:
  - `$msg`: The message to log
- **Actions**:
  1. Prints the message to standard output
  2. Appends the message to the internal status tracker with HTML formatting
  3. Logs the message using the configured logger instance

## Database Interactions

The class primarily interacts with the database through the following methods of the AisDao class:

### 1. getAllActiveDescVehicles()

This method queries the database for all active described vehicles and is used in the standard export process:

- **Query Target**: Primarily targets the `tblDescribedVehicles` table
- **Filtering**: Retrieves only records where `tblStatus_StatusID = 3` (Active status)
- **Joins**: Performs multiple joins to related tables including:
  - `tblVehicleElements` (multiple times via subqueries) to retrieve descriptive text for vehicle attributes
  - `tblCountries` to include country information
- **Data Retrieved**: Comprehensive vehicle data including:
  - Vehicle identifiers (DescVehicleID, ACode)
  - Base information (Year, Make, Model)
  - Body details (Body style, Door count)
  - Engine specifications (Size, Fuel type, Block type, Cylinders, etc.)
  - Transmission details (Type, Speeds)
  - Other specifications (Drive type, Wheel base, Package, Trim level)
  - Manufacturer-specific codes (Model codes, Engine codes, etc.)
- **Query Structure**: Uses multiple subqueries to efficiently retrieve text descriptions from the `tblVehicleElements` table for each vehicle attribute
- **Result Format**: Returns a complete array of vehicle records with all attributes needed for the CSV export

### 2. getAllDataOneActiveDescVehiclesPlusVehicleGroups()

This specialized method extends the standard vehicle query by adding vehicle group information specifically for the DataOne integration:

- **Query Target**: Builds upon the `tblDescribedVehicles` base but extends with vehicle group associations
- **Additional Joins**: Includes joins to:
  - `tblDescVehGrpAssoc` to link vehicles to their vehicle groups
  - `tblVehicleGroups` to get vehicle group information
- **Enhanced Data**: Returns standard vehicle data plus:
  - Vehicle group IDs the vehicle belongs to
  - Vehicle group names
  - Group-specific identifiers and attributes
- **Format Differences**: Structures the data with headers appropriate for the DataOne CSV format
- **Purpose**: Specifically designed to meet the data requirements of the DataOne integration partner

## File Operations

The class performs several file operations:

1. **CSV Generation**: Creates formatted CSV files with proper field separators and enclosures
2. **File Compression**: Uses the PHP ZipArchive class to compress CSV files
3. **File Cleanup**: Removes temporary files after successful operations
4. **Directory Management**: Changes to appropriate directories for file operations

## Error Handling

Error handling is implemented at multiple levels:

1. **Database Queries**: Checks for empty result sets from database queries
2. **File Operations**: Validates file creation and compression success
3. **FTP Transmission**: Checks connection status and file transfer completion
4. **Email Delivery**: Uses try/catch blocks to handle email delivery exceptions

## Configuration Dependencies

The class relies on several configuration parameters:

1. **INIT::$ActiveDescVehSaveDirectory**: Directory path for saving generated files
2. **INIT::$ActiveDescVehRecipients**: Email recipients for data distribution
3. **INIT::$DataOneFtpCredentials**: FTP connection details for DataOne integration
4. **INIT::$LIBS_DIR_PATH**: Path to required libraries

## Key Dependencies

1. **BaseAction**: Parent class providing core functionality
2. **AisDao**: Data Access Object for database operations
3. **PHPMailer**: External library for email functionality
4. **ZipArchive**: PHP extension for file compression
5. **Log4PHP**: Logging framework for operation tracking

## Performance Considerations

The class implements several performance optimizations:

1. **Memory Management**: Unsets large data arrays after use
2. **Directory Handling**: Restores original directory context after operations
3. **Connection Management**: Properly closes FTP connections
4. **Error Recovery**: Attempts to continue operation after non-fatal errors