using AuthoringDataAccessLibrary.Context;
using AuthoringDataAccessLibrary.Interface;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System.Data;
using VehicleExportWorkerService.Data;
using VehicleExportWorkerService.Models;
using VehicleExportWorkerService.Services;

namespace VehicleExportWorkerService
{
    public class Program
    {
        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration(ConfigureApp)
                .UseSerilog(ConfigureLogging)
                .ConfigureServices(ConfigureServices);

        private static void ConfigureServices(HostBuilderContext hostContext, IServiceCollection services)
        {
            var appSettingsSection = hostContext.Configuration.GetSection("AppSettings");
            var appSettings = appSettingsSection.Get<AppSettings>();

            if (appSettings == null)
            {
                throw new InvalidOperationException("AppSettings configuration is missing.");
            }

            services.Configure<AppSettings>(appSettingsSection);

            // Load DbConnectionInfo from configuration
            var dbConnInfo = hostContext.Configuration.GetSection("DbConnectionInfo").Get<DbConnectionInfo>();
            var connectionString = dbConnInfo.GetConnectionString();

            // Register the concrete DbContext
            services.AddDbContext<AuthoringDbContext>(options =>
                options.UseMySql(
                    connectionString,
                    ServerVersion.AutoDetect(connectionString)
                ));

            // Register the interface to resolve to the concrete implementation
            services.AddScoped<IAuthoringDbContext>(provider => provider.GetRequiredService<AuthoringDbContext>());
            // Register other services
            services.AddSingleton<IDbConnection>(sp => new DatabaseContext(hostContext.Configuration).CreateConnection(connectionString));
            services.AddTransient<IAisDataDao, AisDataDao>();
            services.AddTransient<IActiveDescVehExport, ActiveDescVehExport>();
            services.AddTransient<IEmailService, EmailService>();
            services.AddTransient<IWorker, Worker>();
               }

        private static void ConfigureLogging(HostBuilderContext hostContext, LoggerConfiguration configuration)
        {
            configuration.ReadFrom.Configuration(hostContext.Configuration);
        }

        private static void ConfigureApp(HostBuilderContext hostContext, IConfigurationBuilder configuration)
        {
            var environment = hostContext.HostingEnvironment.EnvironmentName;

            configuration
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile($"appsettings.{environment}.json", optional: true)
                .AddEnvironmentVariables();
        }

        public static void Main(string[] args)
        {
            Log.Information("Starting up");
            string environment = Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT", EnvironmentVariableTarget.Machine) ?? "Development";
            var host = CreateHostBuilder(args).UseEnvironment(environment).Build();
            var logger = host.Services.GetRequiredService<ILogger<Program>>();

            try
            {
                using (var scope = host.Services.CreateScope())
                {
                    var worker = scope.ServiceProvider.GetRequiredService<IWorker>();
                    worker.ExecuteAsync(CancellationToken.None).GetAwaiter().GetResult();
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An unhandled exception occurred in the application.");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }
    }
}