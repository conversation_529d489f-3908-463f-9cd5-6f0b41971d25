﻿using System.IO.Compression;
using VehicleExportWorkerService.Data;

namespace VehicleExportWorkerService.Services;

public class ActiveDescVehExport : IActiveDescVehExport
{
    private readonly ILogger<IActiveDescVehExport> _logger;
    private readonly IAisDataDao _aisDataDao;
  
    public ActiveDescVehExport(ILogger<ActiveDescVehExport> logger, IAisDataDao aisDataDao)
    {
        _logger = logger;
        _aisDataDao = aisDataDao;
    }



    public async Task<string> CreateActiveDescVehFileAsync(string saveDirectory)
    {
        var actDescVehs = await _aisDataDao.GetAllActiveDescVehiclesAsync();
        if (actDescVehs.Count == 0)
        {
            _logger.LogInformation("No active described vehicles found.");
            return string.Empty;
        }

        var csvFileName = Path.Combine(saveDirectory, $"ActiveDescribedVehicles_{DateTime.Now:yyyy-MM-dd}.csv");
        await using (var writer = new StreamWriter(csvFileName))
        {
            // No headers, matching PHP implementation
            // Write each vehicle's data
            foreach (var vehicle in actDescVehs)
            {
                var line = string.Join(",",
                    vehicle.DescVehicleID,
                    vehicle.Division,
                    vehicle.Year,
                    vehicle.Model,
                    vehicle.ManufactModelCode,
                    vehicle.ACode,
                    vehicle.Trim,
                    vehicle.Package,
                    vehicle.Body,
                    vehicle.Doors,
                    vehicle.DriveType,
                    vehicle.BlockType,
                    vehicle.Cylinders,
                    vehicle.Aspiration,
                    vehicle.Induction,
                    vehicle.Trans,
                    vehicle.TranSpds,
                    vehicle.BedLength,
                    vehicle.WheelBase,
                    vehicle.OptionGroup,
                    vehicle.EngSize,
                    vehicle.Fuel,
                    vehicle.MfrEngCode,
                    vehicle.MfrTransCode,
                    vehicle.Style_Name
                );
                await writer.WriteLineAsync(line);
            }
        }

        var zipFileName = Path.Combine(saveDirectory, $"ActiveDescribedVehicles_{DateTime.Now:yyyy-MM-dd}.zip");

        // Check if the zip file already exists and delete it to avoid errors
        if (File.Exists(zipFileName))
        {
            _logger.LogInformation("File {Path} already exists. Deleting existing file.", Path.GetFileName(zipFileName));
            File.Delete(zipFileName);
        }

        using (var zip = ZipFile.Open(zipFileName, ZipArchiveMode.Create))
        {
            zip.CreateEntryFromFile(csvFileName, Path.GetFileName(csvFileName));
        }

        File.Delete(csvFileName);
        _logger.LogInformation("File on {Path} created.", Path.GetFileName(zipFileName));

        return zipFileName;
    }
}