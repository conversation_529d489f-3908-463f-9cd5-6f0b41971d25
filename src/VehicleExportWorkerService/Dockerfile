# Multi-stage Dockerfile optimized for ECS deployment
# Use the official .NET 8 SDK image for build
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Set ARGs and ENVs for private repositories
ARG ARTIFACTORY_USERNAME
ENV ARTIFACTORY_USERNAME=${ARTIFACTORY_USERNAME}

ARG ARTIFACTORY_API_KEY
ENV ARTIFACTORY_API_KEY=${ARTIFACTORY_API_KEY}

# Copy project files for dependency restoration
COPY src/VehicleExportWorkerService/VehicleExportWorkerService.csproj ./VehicleExportWorkerService/
COPY src/VehicleExportWorkerService/NuGet.Config ./VehicleExportWorkerService/

# Restore dependencies
WORKDIR /src/VehicleExportWorkerService
RUN dotnet restore --configfile ./NuGet.Config

# Copy source code and build
COPY src/VehicleExportWorkerService/ ./
RUN dotnet build -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish -c Release -o /app/publish --no-restore

# Use ASP.NET Core Runtime for the final image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser -u 1000 appuser

# Create application directory and set permissions
WORKDIR /app
RUN mkdir -p /app/data && chown -R appuser:appuser /app

# Copy published application
COPY --from=publish /app/publish .
RUN chown -R appuser:appuser /app

# Set environment variables
ENV DOTNET_ENVIRONMENT=Production
ENV ASPNETCORE_ENVIRONMENT=Production
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD dotnet --info || exit 1

ENTRYPOINT ["dotnet", "VehicleExportWorkerService.dll"]