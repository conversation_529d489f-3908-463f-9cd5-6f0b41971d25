﻿using System.Data;
using MySql.Data.MySqlClient;

namespace VehicleExportWorkerService.Data
{
    public class DatabaseContext
    {
        private readonly IConfiguration _configuration;

        public DatabaseContext(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public IDbConnection CreateConnection(string connectionString)
        {
             if (string.IsNullOrEmpty(connectionString))
              {
                  throw new InvalidOperationException("Database connection string is not configured.");
              }

             Console.WriteLine($"Using connection string: {connectionString}"); // Debugging log
             return new MySqlConnection(connectionString);
        }
    }
}