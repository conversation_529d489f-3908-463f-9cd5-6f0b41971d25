﻿using System.Data;
using AuthoringDataAccessLibrary.Context;
using VehicleExportWorkerService.Models;
using Microsoft.EntityFrameworkCore;

namespace VehicleExportWorkerService.Data
{
    public class AisDataDao : IAisDataDao
    {
        private readonly AuthoringDbContext _context;
        public AisDataDao(AuthoringDbContext context)
        {
            _context = context;
        }
        public async Task<List<ActiveDescVehicle>> GetAllActiveDescVehiclesAsync()
        {
            var year = 2025;

            // Get division names in a single query
            var divisionDict = await _context.Tblmanufactdivisions
                .ToDictionaryAsync(div => div.DivisionId, div => div.DivName);

            var result = await (
                from v in _context.Tbldescribedvehicles
                join m in _context.Tblvehicleelements on v.ModelEid equals m.VehElementId
                join t in _context.Tblvehicleelements on v.TrimEid equals t.VehElementId
                join p in _context.Tblvehicleelements on v.PackageEid equals p.VehElementId
                join d in _context.Tblvehicleelements on v.DoorsEid equals d.VehElementId
                join b in _context.Tblvehicleelements on v.BodyEid equals b.VehElementId
                join dt in _context.Tblvehicleelements on v.DriveTypeEid equals dt.VehElementId
                join es in _context.Tblvehicleelements on v.EngSizeEid equals es.VehElementId
                join f in _context.Tblvehicleelements on v.FuelEid equals f.VehElementId
                join bl in _context.Tblvehicleelements on v.BlockTypeEid equals bl.VehElementId
                join c in _context.Tblvehicleelements on v.CylindersEid equals c.VehElementId
                join a in _context.Tblvehicleelements on v.AspirationEid equals a.VehElementId
                join ind in _context.Tblvehicleelements on v.InductionEid equals ind.VehElementId
                join tr in _context.Tblvehicleelements on v.TransEid equals tr.VehElementId
                join trs in _context.Tblvehicleelements on v.TranSpdsEid equals trs.VehElementId
                join o in _context.Tblvehicleelements on v.OptionGroupEid equals o.VehElementId
                join mec in _context.Tblvehicleelements on v.MfrEngCodeEid equals mec.VehElementId
                join mtc in _context.Tblvehicleelements on v.MfrTransCodeEid equals mtc.VehElementId
                join bedl in _context.Tblvehicleelements on v.BedLengthEid equals bedl.VehElementId
                join wb in _context.Tblvehicleelements on v.WheelBaseEid equals wb.VehElementId
                where v.TblStatusStatusId == 3
                    && v.DvYear != null
                    && v.DvYear.CompareTo(year.ToString()) >= 0 // string comparison, works if DvYear is always 4-digit year
                select new ActiveDescVehicle
                {
                    DescVehicleID = v.DescVehicleId,
                    Division = divisionDict.ContainsKey(v.TblManufactDivisionsDivisionId) ? divisionDict[v.TblManufactDivisionsDivisionId] : null,
                    Year = int.Parse(v.DvYear),
                    Model = m.VehElementDescript,
                    ManufactModelCode = v.ManufactModelCode,
                    ACode = v.Acode,
                    Trim = t.VehElementDescript,
                    Package = p.VehElementDescript,
                    Body = b.VehElementDescript,
                    Doors = d.VehElementDescript,
                    DriveType = dt.VehElementDescript,
                    BlockType = bl.VehElementDescript,
                    Cylinders = c.VehElementDescript,
                    Aspiration = a.VehElementDescript,
                    Induction = ind.VehElementDescript,
                    Trans = tr.VehElementDescript,
                    TranSpds = trs.VehElementDescript,
                    BedLength = bedl.VehElementDescript,
                    WheelBase = wb.VehElementDescript,
                    OptionGroup = o.VehElementDescript,
                    EngSize = es.VehElementDescript,
                    Fuel = f.VehElementDescript,
                    MfrEngCode = mec.VehElementDescript,
                    MfrTransCode = mtc.VehElementDescript
                }
            ).AsNoTracking().ToListAsync();

            return result;
        }
    }
}
                   
