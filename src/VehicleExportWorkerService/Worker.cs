using Microsoft.Extensions.Options;
using VehicleExportWorkerService.Models;
using VehicleExportWorkerService.Services;

namespace VehicleExportWorkerService;

public class Worker : IWorker
{
    private readonly ILogger<IWorker> _logger;
    private readonly IOptions<AppSettings> _settings;
    private readonly IActiveDescVehExport _activeDescVehExport;
    private readonly IEmailService _emailService;
    private DateTime lastTimer { get; set; }

    public Worker(ILogger<Worker> logger, IOptions<AppSettings> settings, IActiveDescVehExport activeDescVehExport, IEmailService emailService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        _activeDescVehExport = activeDescVehExport ?? throw new ArgumentNullException(nameof(activeDescVehExport));
        _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
    }

    public async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Worker running at: {Time}", DateTimeOffset.Now);

        lastTimer = DateTime.Now;

        var activeDescVehFileName = await _activeDescVehExport.CreateActiveDescVehFileAsync(_settings.Value.ActiveDescVehSaveDirectory);

        if (string.IsNullOrEmpty(activeDescVehFileName))
        {
            _logger.LogInformation("No New Active Vehicle File was created");
        }
        else
        {
            if (await _emailService.SendEmailAsync(activeDescVehFileName))
            {
                _logger.LogInformation("Active Described Vehicle File Emailed");
            }
            else
            {
                _logger.LogInformation("No Email Sent");
            }
        }

        var finish = DateTime.Now;
        var totalTime = Math.Round((finish - lastTimer).TotalSeconds, 4);
        _logger.LogDebug("Page generated in {TotalTime} seconds.", totalTime);
        _logger.LogInformation("Active Described Vehicle Export Complete.");
        _logger.LogDebug("Final memory usage: {MemoryUsage}", GC.GetTotalMemory(false));
    }
}