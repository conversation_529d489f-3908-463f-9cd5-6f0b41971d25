﻿{
  "AppSettings": {
    "ActiveDescVehSaveDirectory": "C:\\Projects\\described-vehicle-extraction-worker\\src\\VehicleExportWorkerService\\Files\\",
    "ConnectionStrings": "Server=localhost;Port=3306;Database=digirs_live_dump_us;Uid=root;Pwd=NewPassword123;", // Set this via secrets or environment variable
    "EmailSettings": {
      "SmtpServer": "email-smtp.us-east-1.amazonaws.com",
      "SmtpPort": 25,
      "SmtpUser": "AKIAR5IWDGG6HLXK75EQ",
      "SmtpPassword": "BIaPdzMPB+E8TIsNylMPMEIhkpzzt23A7pTdJFS6Cehu",
      "FromEmail": "<EMAIL>",
      "ToEmails": [ "<EMAIL>", "<EMAIL>" ]
    }
  },
  "DbConnectionInfo": {
    "Host": "localhost",
    "Port": 3306,
    "Database": "digirs_live_dump_us",
    "User": "root",
    "Password": "NewPassword123"
    //"AdditionalOptions": "AllowZeroDateTime=True;ConvertZeroDateTime=True;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [ "Serilog.Sinks.Console" ],
    "MinimumLevel": "Information",
    "WriteTo": [
      { "Name": "Console" }
    ],
    "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId" ],
    "Properties": {
      "Application": "VehicleExportWorkerService"
    }
  }
}